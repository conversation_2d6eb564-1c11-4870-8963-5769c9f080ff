<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时分析修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        textarea {
            width: 100%;
            height: 120px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #console-output {
            background: #2d2d2d;
            color: #ffffff;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 实时分析修复验证测试</h1>
        <p>此测试用于验证realtimeAnalysisManager服务注册修复和架构监控优化的效果。</p>
    </div>

    <div class="test-container">
        <h2>📋 测试项目</h2>
        <div id="test-results">
            <div class="test-result info">正在初始化测试环境...</div>
        </div>
        <button onclick="runTests()">🚀 开始测试</button>
        <button onclick="clearConsole()">🧹 清除日志</button>
    </div>

    <div class="test-container">
        <h2>🧪 实时分析功能测试</h2>
        <textarea id="test-input" placeholder="在此粘贴订单文本进行实时分析测试...">订单编号：2855018463987817199
买家：tb492398435
支付时间：2025-07-30 16:01:14

舒适9座
【接机】
马来西亚-槟城
[出发]槟城国际机场
[抵达]洪腾海滨酒店
约23.8公里

MF8705
[预计抵达]
2025-07-31 18:30:00

苏美恋
真实号：13860431859
---
1成人0儿童

司机姓名：李师傅</textarea>
        <br>
        <button onclick="testRealtimeAnalysis()">🔍 测试实时分析</button>
        <button onclick="testServiceRegistration()">📋 测试服务注册</button>
    </div>

    <div class="test-container">
        <h2>📊 控制台输出</h2>
        <div id="console-output"></div>
    </div>

    <!-- 加载依赖脚本 -->
    <script src="js/services/logger.js"></script>
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/bootstrap/application-bootstrap.js"></script>
    <script src="js/managers/realtime-analysis-manager.js"></script>

    <script>
        let consoleOutput = document.getElementById('console-output');
        let testResults = document.getElementById('test-results');

        // 拦截console输出
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error,
            info: console.info
        };

        ['log', 'warn', 'error', 'info'].forEach(level => {
            console[level] = function(...args) {
                originalConsole[level].apply(console, args);
                const timestamp = new Date().toLocaleTimeString();
                const message = args.join(' ');
                consoleOutput.innerHTML += `[${timestamp}] ${level.toUpperCase()}: ${message}\n`;
                consoleOutput.scrollTop = consoleOutput.scrollHeight;
            };
        });

        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        function addTestResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            testResults.appendChild(div);
        }

        async function runTests() {
            testResults.innerHTML = '';
            addTestResult('🚀 开始测试修复效果...', 'info');

            // 测试1: 检查realtimeAnalysisManager服务是否已注册
            try {
                const serviceLocator = window.OTA?.ServiceLocator || window.ServiceLocator;
                if (serviceLocator) {
                    const realtimeManager = serviceLocator.getService('realtimeAnalysisManager');
                    if (realtimeManager) {
                        addTestResult('✅ realtimeAnalysisManager服务注册成功', 'success');
                    } else {
                        addTestResult('❌ realtimeAnalysisManager服务未找到', 'error');
                    }
                } else {
                    addTestResult('❌ ServiceLocator未找到', 'error');
                }
            } catch (error) {
                addTestResult(`❌ 服务注册测试失败: ${error.message}`, 'error');
            }

            // 测试2: 检查架构监控配置
            try {
                if (window.ArchitectureGuardian || window.OTA?.ArchitectureGuardian) {
                    const guardian = window.ArchitectureGuardian || window.OTA.ArchitectureGuardian;
                    const complexityThreshold = guardian.intelligentDetectors?.complexityDetector?.complexityThreshold;
                    const maxViolations = guardian.config?.realTimeMonitoring?.maxViolationsPerMinute;
                    
                    if (complexityThreshold === 25) {
                        addTestResult('✅ 复杂度阈值已调整为25', 'success');
                    } else {
                        addTestResult(`⚠️ 复杂度阈值为${complexityThreshold}，预期25`, 'warning');
                    }
                    
                    if (maxViolations === 20) {
                        addTestResult('✅ 违规频率限制已调整为20次/分钟', 'success');
                    } else {
                        addTestResult(`⚠️ 违规频率限制为${maxViolations}，预期20`, 'warning');
                    }
                } else {
                    addTestResult('❌ ArchitectureGuardian未找到', 'error');
                }
            } catch (error) {
                addTestResult(`❌ 架构监控配置测试失败: ${error.message}`, 'error');
            }

            // 测试3: 检查geminiService可用性
            try {
                const geminiService = window.OTA?.getService?.('geminiService');
                if (geminiService && typeof geminiService.isAvailable === 'function') {
                    const isAvailable = geminiService.isAvailable();
                    if (isAvailable) {
                        addTestResult('✅ geminiService.isAvailable()方法正常工作', 'success');
                    } else {
                        addTestResult('⚠️ geminiService不可用', 'warning');
                    }
                } else {
                    addTestResult('❌ geminiService.isAvailable()方法未找到或geminiService不可用', 'error');
                }
            } catch (error) {
                addTestResult(`❌ Gemini服务测试失败: ${error.message}`, 'error');
            }

            addTestResult('🎯 测试完成！检查上述结果以确认修复效果。', 'info');
        }

        async function testServiceRegistration() {
            console.log('=== 开始服务注册详细测试 ===');
            
            try {
                // 测试服务定位器
                if (window.OTA && window.OTA.getService) {
                    const realtimeManager = window.OTA.getService('realtimeAnalysisManager');
                    console.log('通过OTA.getService获取realtimeAnalysisManager:', realtimeManager);
                    
                    if (realtimeManager) {
                        console.log('realtimeAnalysisManager方法列表:', Object.keys(realtimeManager));
                        console.log('isAvailable方法存在:', typeof realtimeManager.isAvailable);
                    }
                } else {
                    console.error('OTA.getService方法不可用');
                }
            } catch (error) {
                console.error('服务注册测试失败:', error);
            }
        }

        async function testRealtimeAnalysis() {
            const testText = document.getElementById('test-input').value;
            if (!testText.trim()) {
                addTestResult('⚠️ 请输入测试文本', 'warning');
                return;
            }

            console.log('=== 开始实时分析测试 ===');
            console.log('测试文本长度:', testText.length);
            console.log('测试文本预览:', testText.substring(0, 100) + '...');

            try {
                // 模拟实时分析流程
                const realtimeManager = window.OTA?.getService?.('realtimeAnalysisManager');
                if (realtimeManager && typeof realtimeManager.triggerRealtimeAnalysis === 'function') {
                    console.log('调用triggerRealtimeAnalysis方法...');
                    await realtimeManager.triggerRealtimeAnalysis(testText);
                    addTestResult('✅ 实时分析调用成功', 'success');
                } else {
                    console.warn('realtimeAnalysisManager.triggerRealtimeAnalysis方法不可用');
                    addTestResult('⚠️ 实时分析功能不可用', 'warning');
                }
            } catch (error) {
                console.error('实时分析测试失败:', error);
                addTestResult(`❌ 实时分析测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('🔧 实时分析修复验证测试页面已加载');
                console.log('OTA命名空间状态:', !!window.OTA);
                console.log('ServiceLocator可用:', !!window.OTA?.ServiceLocator);
                console.log('getService方法可用:', typeof window.OTA?.getService);
                
                runTests();
            }, 2000);
        });
    </script>
</body>
</html>