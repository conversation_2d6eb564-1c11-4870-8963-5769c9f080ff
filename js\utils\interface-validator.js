/**
 * 接口一致性验证工具
 * 用于防止类似 container.isInitialized 方法缺失的问题
 * <AUTHOR> System
 * @created 2025-01-29
 */

window.OTA = window.OTA || {};
window.OTA.utils = window.OTA.utils || {};

(function() {
    'use strict';

    /**
     * 接口验证器类
     * 用于验证对象是否实现了预期的接口
     */
    class InterfaceValidator {
        constructor() {
            this.interfaces = new Map(); // 存储接口定义
            this.validationResults = new Map(); // 存储验证结果
        }

        /**
         * 注册接口定义
         * @param {string} interfaceName - 接口名称
         * @param {Object} interfaceSpec - 接口规范
         */
        registerInterface(interfaceName, interfaceSpec) {
            this.interfaces.set(interfaceName, interfaceSpec);
            console.log(`[InterfaceValidator] 已注册接口: ${interfaceName}`);
        }

        /**
         * 验证对象是否符合接口规范
         * @param {Object} obj - 要验证的对象
         * @param {string} interfaceName - 接口名称
         * @returns {Object} 验证结果
         */
        validateInterface(obj, interfaceName) {
            const interfaceSpec = this.interfaces.get(interfaceName);
            if (!interfaceSpec) {
                throw new Error(`接口定义不存在: ${interfaceName}`);
            }

            const result = {
                interfaceName,
                isValid: true,
                missingMethods: [],
                missingProperties: [],
                typeErrors: [],
                timestamp: new Date().toISOString()
            };

            // 验证方法
            if (interfaceSpec.methods) {
                for (const methodName of interfaceSpec.methods) {
                    if (typeof obj[methodName] !== 'function') {
                        result.isValid = false;
                        result.missingMethods.push(methodName);
                    }
                }
            }

            // 验证属性
            if (interfaceSpec.properties) {
                for (const [propName, expectedType] of Object.entries(interfaceSpec.properties)) {
                    if (!(propName in obj)) {
                        result.isValid = false;
                        result.missingProperties.push(propName);
                    } else if (expectedType && typeof obj[propName] !== expectedType) {
                        result.isValid = false;
                        result.typeErrors.push({
                            property: propName,
                            expected: expectedType,
                            actual: typeof obj[propName]
                        });
                    }
                }
            }

            this.validationResults.set(`${interfaceName}_${Date.now()}`, result);
            return result;
        }

        /**
         * 批量验证多个对象
         * @param {Array} validations - 验证配置数组
         * @returns {Array} 验证结果数组
         */
        batchValidate(validations) {
            return validations.map(({ obj, interfaceName, objectName }) => {
                try {
                    const result = this.validateInterface(obj, interfaceName);
                    result.objectName = objectName;
                    return result;
                } catch (error) {
                    return {
                        objectName,
                        interfaceName,
                        isValid: false,
                        error: error.message,
                        timestamp: new Date().toISOString()
                    };
                }
            });
        }

        /**
         * 生成验证报告
         * @returns {Object} 验证报告
         */
        generateReport() {
            const results = Array.from(this.validationResults.values());
            const report = {
                totalValidations: results.length,
                successCount: results.filter(r => r.isValid).length,
                failureCount: results.filter(r => !r.isValid).length,
                results: results,
                generatedAt: new Date().toISOString()
            };

            return report;
        }
    }

    // 预定义核心接口规范
    const CORE_INTERFACES = {
        'DependencyContainer': {
            methods: ['register', 'resolve', 'isInitialized', 'getStatus'],
            properties: {
                'initialized': 'boolean'
            }
        },
        'ServiceLocator': {
            methods: ['get', 'register', 'init'],
            properties: {}
        },
        'Registry': {
            methods: ['getRegistryInfo', 'register', 'unregister'],
            properties: {}
        },
        'Logger': {
            methods: ['log', 'error', 'warn', 'debug'],
            properties: {}
        }
    };

    // 创建全局验证器实例
    const validator = new InterfaceValidator();

    // 注册核心接口
    Object.entries(CORE_INTERFACES).forEach(([name, spec]) => {
        validator.registerInterface(name, spec);
    });

    /**
     * 系统启动时的接口验证
     */
    function validateSystemInterfaces() {
        console.log('[InterfaceValidator] 开始系统接口验证...');

        const validations = [
            {
                obj: window.OTA?.container,
                interfaceName: 'DependencyContainer',
                objectName: 'window.OTA.container'
            },
            {
                obj: window.OTA?.Registry,
                interfaceName: 'Registry',
                objectName: 'window.OTA.Registry'
            }
        ];

        const results = validator.batchValidate(validations);
        
        // 输出验证结果
        results.forEach(result => {
            if (result.isValid) {
                console.log(`✅ [InterfaceValidator] ${result.objectName} 接口验证通过`);
            } else {
                console.error(`❌ [InterfaceValidator] ${result.objectName} 接口验证失败:`, result);
                
                // 详细错误信息
                if (result.missingMethods?.length > 0) {
                    console.error(`   缺失方法: ${result.missingMethods.join(', ')}`);
                }
                if (result.missingProperties?.length > 0) {
                    console.error(`   缺失属性: ${result.missingProperties.join(', ')}`);
                }
                if (result.typeErrors?.length > 0) {
                    result.typeErrors.forEach(error => {
                        console.error(`   类型错误: ${error.property} 期望 ${error.expected}, 实际 ${error.actual}`);
                    });
                }
            }
        });

        return results;
    }

    // 导出到全局命名空间
    window.OTA.utils.InterfaceValidator = InterfaceValidator;
    window.OTA.utils.interfaceValidator = validator;
    window.OTA.utils.validateSystemInterfaces = validateSystemInterfaces;

    console.log('✅ 接口验证工具已加载');

})();
