/**
 * 工具函数模块
 * 提供通用的工具函数和辅助方法
 * 重构为传统script标签加载方式
 */

// 创建OTA命名空间
window.OTA = window.OTA || {};

(function() {
    'use strict';

/**
 * 防抖函数
 * @param {function} func - 需要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {function} 防抖后的函数
 */
function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(this, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(this, args);
    };
}

/**
 * 节流函数
 * @param {function} func - 需要节流的函数
 * @param {number} limit - 时间间隔（毫秒）
 * @returns {function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 深拷贝对象
 * @param {any} obj - 需要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }
    
    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }
    
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
    
    return obj;
}

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @param {string} format - 格式字符串
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format = 'YYYY-MM-DD') {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d)) return '';
    
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    const replacements = {
        'YYYY': year,
        'MM': month,
        'DD': day,
        'HH': hours,
        'mm': minutes,
        'ss': seconds
    };
    
    let result = format;
    Object.entries(replacements).forEach(([key, value]) => {
        result = result.replace(new RegExp(key, 'g'), value);
    });
    
    return result;
}

/**
 * 格式化时间
 * @param {Date} date - 日期对象
 * @param {boolean} includeSeconds - 是否包含秒
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(date, includeSeconds = false) {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d)) return '';
    
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    
    if (includeSeconds) {
        const seconds = String(d.getSeconds()).padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
    }
    
    return `${hours}:${minutes}`;
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 格式化价格显示
 * @param {number|string} price - 价格数值
 * @param {string} currency - 货币类型
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化的价格字符串
 */
function formatPrice(price, currency = 'MYR', decimals = 2) {
    // 处理空值或无效值
    if (!price || price === '' || price === null || price === undefined) {
        return `${currency} 0.${'0'.repeat(decimals)}`;
    }

    // 转换为数字
    const numPrice = parseFloat(price);
    if (isNaN(numPrice)) {
        return `${currency} 0.${'0'.repeat(decimals)}`;
    }

    // 货币符号映射
    const symbols = {
        'MYR': 'RM',
        'CNY': '￥',
        'USD': '$',
        'SGD': 'S$'
    };

    const symbol = symbols[currency] || currency;
    return `${symbol} ${numPrice.toFixed(decimals)}`;
}

/**
 * 格式化电话号码显示（隐私保护）
 * @param {string} phone - 电话号码
 * @param {number} displayLength - 显示长度
 * @returns {string} 格式化的电话号码
 */
function formatPhoneDisplay(phone, displayLength = 6) {
    if (!phone) return '未提供';

    // 格式化电话显示：+6012***
    if (phone.startsWith('+60')) {
        return phone.substr(0, 5) + '***';
    }

    return phone.length > displayLength ?
        phone.substr(0, displayLength) + '***' : phone;
}

/**
 * 格式化日期为API所需格式 (DD-MM-YYYY)
 * @param {string|Date} dateValue - 日期值
 * @returns {string} 格式化后的日期
 */
function formatDateForAPI(dateValue) {
    if (!dateValue) return '';

    try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) return dateValue;

        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        return `${day}-${month}-${year}`;
    } catch (error) {
        return dateValue;
    }
}

/**
 * 格式化日期为HTML input所需格式 (YYYY-MM-DD)
 * @param {string|Date} dateValue - 日期值
 * @returns {string} 格式化后的日期
 */
function formatDateForInput(dateValue) {
    if (!dateValue) return '';

    try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) {
            return new Date().toISOString().split('T')[0];
        }
        return date.toISOString().split('T')[0];
    } catch (error) {
        return new Date().toISOString().split('T')[0];
    }
}

/**
 * 生成唯一ID
 * @param {string} prefix - 前缀
 * @returns {string} 唯一ID
 */
function generateId(prefix = '') {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substr(2, 9);
    return prefix ? `${prefix}_${timestamp}_${randomStr}` : `${timestamp}_${randomStr}`;
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否有效
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * 验证电话号码格式
 * @param {string} phone - 电话号码
 * @returns {boolean} 是否有效
 */
function isValidPhone(phone) {
    if (!phone) return false;

    // 支持多种电话号码格式
    const patterns = [
        /^(\+\d{1,3})?[1-9]\d{7,14}$/,  // 国际格式
        /^\d{3,4}-\d{7,8}$/,            // 固定电话格式
        /^1[3-9]\d{9}$/,                // 中国手机号
        /^01[0-9]\d{7,8}$/,             // 马来西亚手机号
        /^[89]\d{7}$/                   // 新加坡手机号
    ];

    return patterns.some(pattern => pattern.test(phone.replace(/\s/g, '')));
}

/**
 * 验证日期格式
 * @param {string} dateStr - 日期字符串
 * @param {string} format - 期望格式 ('DD-MM-YYYY' | 'YYYY-MM-DD')
 * @returns {boolean} 是否有效
 */
function isValidDate(dateStr, format = 'DD-MM-YYYY') {
    if (!dateStr) return false;

    const patterns = {
        'DD-MM-YYYY': /^\d{2}-\d{2}-\d{4}$/,
        'YYYY-MM-DD': /^\d{4}-\d{2}-\d{2}$/,
        'DD/MM/YYYY': /^\d{2}\/\d{2}\/\d{4}$/,
        'YYYY/MM/DD': /^\d{4}\/\d{2}\/\d{2}$/
    };

    const pattern = patterns[format];
    if (!pattern || !pattern.test(dateStr)) return false;

    // 验证日期是否真实存在
    try {
        const date = new Date(dateStr);
        return !isNaN(date.getTime());
    } catch {
        return false;
    }
}

/**
 * 验证时间格式
 * @param {string} timeStr - 时间字符串
 * @returns {boolean} 是否有效
 */
function isValidTime(timeStr) {
    if (!timeStr) return false;

    const patterns = [
        /^([01]?\d|2[0-3]):[0-5]\d$/,   // HH:MM
        /^([01]?\d|2[0-3])[0-5]\d$/     // HHMM
    ];

    return patterns.some(pattern => pattern.test(timeStr));
}

/**
 * 验证价格格式
 * @param {string|number} price - 价格
 * @returns {boolean} 是否有效
 */
function isValidPrice(price) {
    if (price === null || price === undefined || price === '') return false;

    const numPrice = parseFloat(price);
    return !isNaN(numPrice) && numPrice >= 0;
}

/**
 * 标准化电话号码（保持原始格式，不自动添加国家代码）
 * @param {string} phone - 原始电话号码
 * @returns {string} 标准化后的电话号码
 */
function normalizePhoneNumber(phone) {
    if (!phone) return '';

    // 只移除多余的空格，保持原始格式
    let cleaned = phone.trim();

    // 移除多余的空格，但保持基本格式
    cleaned = cleaned.replace(/\s+/g, ' ');

    return cleaned;
}

/**
 * 解析日期字符串
 * @param {string} dateStr - 日期字符串
 * @returns {Date|null} 解析后的日期对象
 */
function parseDate(dateStr) {
    if (!dateStr) return null;
    
    try {
        // 尝试直接解析
        let date = new Date(dateStr);
        if (!isNaN(date)) return date;
        
        // 尝试各种格式
        const formats = [
            /^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/, // DD/MM/YYYY or DD-MM-YYYY
            /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/, // YYYY/MM/DD or YYYY-MM-DD
            /^(\d{1,2})[-\/](\d{1,2})[-\/](\d{2})$/   // DD/MM/YY or DD-MM-YY
        ];
        
        for (const format of formats) {
            const match = dateStr.match(format);
            if (match) {
                let [, part1, part2, part3] = match;
                
                // 判断格式并创建日期
                if (part3.length === 4) {
                    // 年份在最后，假设是DD/MM/YYYY
                    date = new Date(part3, part2 - 1, part1);
                } else if (part1.length === 4) {
                    // 年份在最前，是YYYY/MM/DD
                    date = new Date(part1, part2 - 1, part3);
                } else {
                    // 两位年份，假设是DD/MM/YY
                    const year = parseInt(part3) < 50 ? 2000 + parseInt(part3) : 1900 + parseInt(part3);
                    date = new Date(year, part2 - 1, part1);
                }
                
                if (!isNaN(date)) return date;
            }
        }
        
        return null;
    } catch (error) {
        logger.log('日期解析失败', 'error', { dateStr, error: error.message });
        return null;
    }
}

/**
 * 转换日期格式为DD-MM-YYYY
 * @param {string} dateStr - 输入日期字符串
 * @returns {string} DD-MM-YYYY格式的日期
 */
function convertToApiDateFormat(dateStr) {
    if (!dateStr) return '';
    
    const date = parseDate(dateStr);
    if (!date) return dateStr;
    
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}-${month}-${year}`;
}

/**
 * 计算两个日期之间的天数差
 * @param {Date} date1 - 第一个日期
 * @param {Date} date2 - 第二个日期
 * @returns {number} 天数差
 */
function daysDifference(date1, date2) {
    const oneDay = 24 * 60 * 60 * 1000;
    return Math.round(Math.abs((date1 - date2) / oneDay));
}

/**
 * 检查是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
function isMobile() {
    return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

/**
 * 获取浏览器信息
 * @returns {object} 浏览器信息
 */
function getBrowserInfo() {
    const ua = navigator.userAgent;
    let browser = 'Unknown';
    let version = 'Unknown';
    
    if (ua.includes('Chrome')) {
        browser = 'Chrome';
        version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
    } else if (ua.includes('Firefox')) {
        browser = 'Firefox';
        version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
    } else if (ua.includes('Safari')) {
        browser = 'Safari';
        version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown';
    } else if (ua.includes('Edge')) {
        browser = 'Edge';
        version = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
    }
    
    return {
        browser,
        version,
        userAgent: ua,
        isMobile: isMobile(),
        language: navigator.language,
        platform: navigator.platform
    };
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 需要复制的文本
 * @returns {Promise<boolean>} 是否成功
 */
async function copyToClipboard(text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            const success = document.execCommand('copy');
            document.body.removeChild(textArea);
            return success;
        }
    } catch (error) {
        logger.log('复制到剪贴板失败', 'error', { text, error: error.message });
        return false;
    }
}

/**
 * 下载文件
 * @param {string} content - 文件内容
 * @param {string} filename - 文件名
 * @param {string} mimeType - MIME类型
 */
function downloadFile(content, filename, mimeType = 'text/plain') {
    try {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        
        logger.logUserAction('文件下载', { filename, size: blob.size });
    } catch (error) {
        logger.log('文件下载失败', 'error', { filename, error: error.message });
        throw error;
    }
}

/**
 * 解析URL参数
 * @param {string} url - URL字符串（可选，默认为当前页面URL）
 * @returns {object} 参数对象
 */
function parseUrlParams(url = window.location.href) {
    const params = {};
    const urlObj = new URL(url);
    
    for (const [key, value] of urlObj.searchParams) {
        params[key] = value;
    }
    
    return params;
}

/**
 * 构建URL参数字符串
 * @param {object} params - 参数对象
 * @returns {string} 参数字符串
 */
function buildUrlParams(params) {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
            searchParams.append(key, String(value));
        }
    });
    
    return searchParams.toString();
}

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {any} defaultValue - 默认值
 * @returns {any} 解析结果
 */
function safeJsonParse(jsonString, defaultValue = null) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        logger.log('JSON解析失败', 'error', { jsonString, error: error.message });
        return defaultValue;
    }
}

/**
 * 安全的JSON字符串化
 * @param {any} value - 需要字符串化的值
 * @param {string} defaultValue - 默认值
 * @returns {string} JSON字符串
 */
function safeJsonStringify(value, defaultValue = '{}') {
    try {
        return JSON.stringify(value);
    } catch (error) {
        logger.log('JSON字符串化失败', 'error', { value, error: error.message });
        return defaultValue;
    }
}

/**
 * 等待指定时间
 * @param {number} ms - 等待时间（毫秒）
 * @returns {Promise} Promise对象
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 重试函数
 * @param {function} fn - 需要重试的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delay - 重试间隔（毫秒）
 * @returns {Promise} Promise对象
 */
async function retry(fn, maxRetries = 3, delay = 1000) {
    let lastError;
    
    for (let i = 0; i <= maxRetries; i++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error;
            logger.log(`重试第${i + 1}次失败`, 'error', { error: error.message, attempt: i + 1 });
            
            if (i < maxRetries) {
                await sleep(delay * Math.pow(2, i)); // 指数退避
            }
        }
    }
    
    throw lastError;
}

/**
 * 对象差异比较
 * @param {object} obj1 - 第一个对象
 * @param {object} obj2 - 第二个对象
 * @returns {object} 差异对象
 */
function objectDiff(obj1, obj2) {
    const diff = {};
    
    // 检查obj1中的字段
    Object.keys(obj1).forEach(key => {
        if (obj1[key] !== obj2[key]) {
            diff[key] = {
                old: obj1[key],
                new: obj2[key]
            };
        }
    });
    
    // 检查obj2中新增的字段
    Object.keys(obj2).forEach(key => {
        if (!(key in obj1)) {
            diff[key] = {
                old: undefined,
                new: obj2[key]
            };
        }
    });
    
    return diff;
}

/**
 * 数组去重
 * @param {Array} array - 原数组
 * @param {string} key - 去重依据的键（对象数组时使用）
 * @returns {Array} 去重后的数组
 */
function uniqueArray(array, key = null) {
    if (!Array.isArray(array)) return [];
    
    if (key) {
        const seen = new Set();
        return array.filter(item => {
            const value = item[key];
            if (seen.has(value)) {
                return false;
            }
            seen.add(value);
            return true;
        });
    }
    
    return [...new Set(array)];
}

/**
 * 字符串截断
 * @param {string} str - 原字符串
 * @param {number} length - 截断长度
 * @param {string} suffix - 后缀
 * @returns {string} 截断后的字符串
 */
function truncateString(str, length, suffix = '...') {
    if (!str || str.length <= length) return str;
    return str.substring(0, length) + suffix;
}

/**
 * 驼峰命名转下划线命名
 * @param {string} str - 驼峰命名字符串
 * @returns {string} 下划线命名字符串
 */
function camelToSnake(str) {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

/**
 * 下划线命名转驼峰命名
 * @param {string} str - 下划线命名字符串
 * @returns {string} 驼峰命名字符串
 */
function snakeToCamel(str) {
    return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
}

/**
 * 检查对象是否为空
 * @param {any} obj - 检查的对象
 * @returns {boolean} 是否为空
 */
function isEmpty(obj) {
    if (obj == null) return true;
    if (Array.isArray(obj)) return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    if (typeof obj === 'string') return obj.trim().length === 0;
    return false;
}

/**
 * 获取对象的嵌套属性值
 * @param {object} obj - 对象
 * @param {string} path - 属性路径，如 'user.profile.name'
 * @param {any} defaultValue - 默认值
 * @returns {any} 属性值
 */
function getNestedValue(obj, path, defaultValue = undefined) {
    if (!obj || !path) return defaultValue;
    
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
        if (current == null || typeof current !== 'object') {
            return defaultValue;
        }
        current = current[key];
    }
    
    return current !== undefined ? current : defaultValue;
}

/**
 * 设置对象的嵌套属性值
 * @param {object} obj - 对象
 * @param {string} path - 属性路径
 * @param {any} value - 新值
 * @returns {object} 修改后的对象
 */
function setNestedValue(obj, path, value) {
    if (!obj || !path) return obj;
    
    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = obj;
    
    for (const key of keys) {
        if (!(key in current) || typeof current[key] !== 'object') {
            current[key] = {};
        }
        current = current[key];
    }
    
    current[lastKey] = value;
    return obj;
}

// 性能监控工具
class PerformanceMonitor {
    constructor() {
        this.marks = new Map();
        this.measures = new Map();
    }
    
    /**
     * 开始性能标记
     * @param {string} name - 标记名称
     */
    mark(name) {
        const startTime = performance.now();
        this.marks.set(name, startTime);
        // 延迟调用logger，确保logger已加载
        if (window.OTA && window.OTA.logger) {
            window.OTA.logger.log(`性能标记开始: ${name}`, 'debug', { startTime });
        }
    }
    
    /**
     * 结束性能标记并返回耗时
     * @param {string} name - 标记名称
     * @returns {number} 耗时（毫秒）
     */
    measure(name) {
        const endTime = performance.now();
        const startTime = this.marks.get(name);
        
        if (!startTime) {
            if (window.OTA && window.OTA.logger) {
                window.OTA.logger.log(`性能标记 ${name} 不存在`, 'error');
            }
            return 0;
        }

        const duration = endTime - startTime;
        this.measures.set(name, duration);
        this.marks.delete(name);

        if (window.OTA && window.OTA.logger) {
            window.OTA.logger.log(`性能测量完成: ${name}`, 'debug', {
                duration: `${duration.toFixed(2)}ms`,
                startTime,
                endTime
            });
        }
        
        return duration;
    }
    
    /**
     * 获取性能统计
     * @returns {object} 性能统计信息
     */
    getStats() {
        return {
            measures: Object.fromEntries(this.measures),
            activeMark: Array.from(this.marks.keys())
        };
    }
    
    /**
     * 清空性能数据
     */
    clear() {
        this.marks.clear();
        this.measures.clear();
    }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor();

    // 暴露到OTA命名空间
    window.OTA.utils = {
        debounce,
        throttle,
        deepClone,
        formatDate,
        formatTime,
        formatFileSize,
        formatPrice,
        formatPhoneDisplay,
        formatDateForAPI,
        formatDateForInput,
        generateId,
        isValidEmail,
        isValidPhone,
        isValidDate,
        isValidTime,
        isValidPrice,
        normalizePhoneNumber,
        parseDate,
        convertToApiDateFormat,
        daysDifference,
        isMobile,
        getBrowserInfo,
        copyToClipboard,
        downloadFile,
        parseUrlParams,
        buildUrlParams,
        safeJsonParse,
        safeJsonStringify,
        sleep,
        retry,
        objectDiff,
        uniqueArray,
        truncateString,
        camelToSnake,
        snakeToCamel,
        isEmpty,
        getNestedValue,
        setNestedValue,
        performanceMonitor
    };

    // 向后兼容：暴露到全局window对象（带废弃警告）
    Object.defineProperty(window, 'utils', {
        get() {
            console.warn('[DEPRECATED] window.utils 已废弃，请使用 window.OTA.utils');
            return window.OTA.utils;
        },
        configurable: true
    });

})();