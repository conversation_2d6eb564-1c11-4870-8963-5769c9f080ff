/**
 * 性能基准测试
 * 验证架构优化后的性能改进效果
 * 
 * 测试范围:
 * - 内存使用优化验证
 * - DOM查询性能优化验证
 * - 服务访问性能测试
 * - 配置获取性能测试
 * - 模块加载时间测试
 * - 事件处理性能测试
 * - 垃圾回收影响测试
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.tests = window.OTA.tests || {};

(function() {
    'use strict';

    /**
     * 性能基准测试类
     */
    class PerformanceBenchmarkTest {
        constructor() {
            this.benchmarkResults = {
                memory: {},
                domQueries: {},
                serviceAccess: {},
                configAccess: {},
                moduleLoading: {},
                eventHandling: {},
                garbageCollection: {},
                overall: {}
            };
            
            this.testConfig = {
                iterations: 1000,
                warmupIterations: 100,
                memoryMeasurementInterval: 100,
                performanceMarkPrefix: 'OTA_PERF_',
                thresholds: {
                    memoryUsage: 100 * 1024 * 1024, // 100MB
                    domQueryTime: 10, // 10ms
                    serviceAccessTime: 1, // 1ms
                    configAccessTime: 0.5, // 0.5ms
                    moduleLoadTime: 50, // 50ms
                    eventHandlingTime: 5, // 5ms
                    gcPauseTime: 20 // 20ms
                }
            };
            
            this.performanceObserver = null;
            this.memoryMonitor = null;
            this.initialize();
        }

        /**
         * 初始化性能测试系统
         */
        initialize() {
            // 设置性能观察器
            if (window.PerformanceObserver) {
                this.performanceObserver = new PerformanceObserver((list) => {
                    this.handlePerformanceEntries(list.getEntries());
                });
                
                this.performanceObserver.observe({ 
                    entryTypes: ['measure', 'mark', 'navigation', 'resource'] 
                });
            }
            
            // 启动内存监控
            this.startMemoryMonitoring();
            
            console.log('✅ 性能基准测试已初始化');
        }

        /**
         * 运行所有性能基准测试
         */
        async runAllBenchmarks() {
            console.group('⚡ 开始性能基准测试');
            
            try {
                // 预热阶段
                await this.warmupPhase();
                
                // 内存使用基准测试
                await this.benchmarkMemoryUsage();
                
                // DOM查询性能基准测试
                await this.benchmarkDOMQueries();
                
                // 服务访问性能基准测试
                await this.benchmarkServiceAccess();
                
                // 配置访问性能基准测试
                await this.benchmarkConfigAccess();
                
                // 模块加载性能基准测试
                await this.benchmarkModuleLoading();
                
                // 事件处理性能基准测试
                await this.benchmarkEventHandling();
                
                // 垃圾回收影响基准测试
                await this.benchmarkGarbageCollection();
                
                // 计算总体性能分数
                this.calculateOverallPerformance();
                
                // 生成性能报告
                const report = this.generatePerformanceReport();
                
                console.log('📊 性能基准测试完成');
                console.log('性能报告:', report);
                
                return report;
                
            } catch (error) {
                console.error('性能基准测试失败:', error);
                throw error;
            } finally {
                console.groupEnd();
            }
        }

        /**
         * 预热阶段
         */
        async warmupPhase() {
            console.log('🔥 执行预热阶段...');
            
            // 预热服务访问
            for (let i = 0; i < this.testConfig.warmupIterations; i++) {
                window.OTA?.getService?.('logger');
                window.OTA?.getConfig?.('test.config', 'default');
            }
            
            // 预热DOM查询
            for (let i = 0; i < this.testConfig.warmupIterations; i++) {
                document.querySelectorAll('div');
                document.getElementById('app');
            }
            
            // 等待预热完成
            await this.sleep(100);
            
            console.log('✅ 预热阶段完成');
        }

        /**
         * 内存使用基准测试
         */
        async benchmarkMemoryUsage() {
            console.log('💾 测试内存使用性能...');
            
            const results = {
                initialMemory: 0,
                peakMemory: 0,
                finalMemory: 0,
                memoryGrowth: 0,
                memoryEfficiency: 0,
                passed: false
            };
            
            if (performance.memory) {
                // 记录初始内存
                results.initialMemory = performance.memory.usedJSHeapSize;
                
                // 执行内存密集操作
                const memoryTestData = [];
                const startTime = performance.now();
                
                for (let i = 0; i < 10000; i++) {
                    memoryTestData.push({
                        id: i,
                        data: new Array(100).fill(Math.random()),
                        timestamp: Date.now()
                    });
                    
                    // 定期检查内存使用
                    if (i % 1000 === 0) {
                        const currentMemory = performance.memory.usedJSHeapSize;
                        results.peakMemory = Math.max(results.peakMemory, currentMemory);
                    }
                }
                
                const endTime = performance.now();
                
                // 清理测试数据
                memoryTestData.length = 0;
                
                // 强制垃圾回收（如果可用）
                if (window.gc) {
                    window.gc();
                }
                
                // 等待垃圾回收
                await this.sleep(100);
                
                // 记录最终内存
                results.finalMemory = performance.memory.usedJSHeapSize;
                results.memoryGrowth = results.finalMemory - results.initialMemory;
                results.memoryEfficiency = (endTime - startTime) / 10000; // ms per operation
                
                // 检查是否通过阈值
                results.passed = results.peakMemory < this.testConfig.thresholds.memoryUsage;
                
                console.log(`内存测试结果: 初始=${this.formatBytes(results.initialMemory)}, 峰值=${this.formatBytes(results.peakMemory)}, 最终=${this.formatBytes(results.finalMemory)}`);
            }
            
            this.benchmarkResults.memory = results;
        }

        /**
         * DOM查询性能基准测试
         */
        async benchmarkDOMQueries() {
            console.log('🔍 测试DOM查询性能...');
            
            const results = {
                averageQueryTime: 0,
                minQueryTime: Infinity,
                maxQueryTime: 0,
                totalQueries: 0,
                queriesPerSecond: 0,
                passed: false
            };
            
            const querySelectors = [
                'div',
                '.container',
                '#app',
                'input[type="text"]',
                'button.btn',
                '*[data-test]'
            ];
            
            const queryTimes = [];
            const startTime = performance.now();
            
            for (let i = 0; i < this.testConfig.iterations; i++) {
                const selector = querySelectors[i % querySelectors.length];
                
                const queryStart = performance.now();
                document.querySelectorAll(selector);
                const queryEnd = performance.now();
                
                const queryTime = queryEnd - queryStart;
                queryTimes.push(queryTime);
                
                results.minQueryTime = Math.min(results.minQueryTime, queryTime);
                results.maxQueryTime = Math.max(results.maxQueryTime, queryTime);
                results.totalQueries++;
            }
            
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            
            results.averageQueryTime = queryTimes.reduce((sum, time) => sum + time, 0) / queryTimes.length;
            results.queriesPerSecond = (results.totalQueries / totalTime) * 1000;
            results.passed = results.averageQueryTime < this.testConfig.thresholds.domQueryTime;
            
            console.log(`DOM查询测试结果: 平均=${results.averageQueryTime.toFixed(3)}ms, QPS=${results.queriesPerSecond.toFixed(0)}`);
            
            this.benchmarkResults.domQueries = results;
        }

        /**
         * 服务访问性能基准测试
         */
        async benchmarkServiceAccess() {
            console.log('🔧 测试服务访问性能...');
            
            const results = {
                averageAccessTime: 0,
                minAccessTime: Infinity,
                maxAccessTime: 0,
                totalAccesses: 0,
                accessesPerSecond: 0,
                cacheHitRate: 0,
                passed: false
            };
            
            const serviceNames = ['logger', 'configCenter', 'performanceMonitor', 'uiManager', 'eventManager'];
            const accessTimes = [];
            let cacheHits = 0;
            
            const startTime = performance.now();
            
            for (let i = 0; i < this.testConfig.iterations; i++) {
                const serviceName = serviceNames[i % serviceNames.length];
                
                const accessStart = performance.now();
                const service = window.OTA?.getService?.(serviceName);
                const accessEnd = performance.now();
                
                const accessTime = accessEnd - accessStart;
                accessTimes.push(accessTime);
                
                if (service) {
                    cacheHits++;
                }
                
                results.minAccessTime = Math.min(results.minAccessTime, accessTime);
                results.maxAccessTime = Math.max(results.maxAccessTime, accessTime);
                results.totalAccesses++;
            }
            
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            
            results.averageAccessTime = accessTimes.reduce((sum, time) => sum + time, 0) / accessTimes.length;
            results.accessesPerSecond = (results.totalAccesses / totalTime) * 1000;
            results.cacheHitRate = (cacheHits / results.totalAccesses) * 100;
            results.passed = results.averageAccessTime < this.testConfig.thresholds.serviceAccessTime;
            
            console.log(`服务访问测试结果: 平均=${results.averageAccessTime.toFixed(3)}ms, APS=${results.accessesPerSecond.toFixed(0)}, 缓存命中率=${results.cacheHitRate.toFixed(1)}%`);
            
            this.benchmarkResults.serviceAccess = results;
        }

        /**
         * 配置访问性能基准测试
         */
        async benchmarkConfigAccess() {
            console.log('⚙️ 测试配置访问性能...');
            
            const results = {
                averageAccessTime: 0,
                minAccessTime: Infinity,
                maxAccessTime: 0,
                totalAccesses: 0,
                accessesPerSecond: 0,
                passed: false
            };
            
            const configKeys = [
                'app.name',
                'app.version',
                'performance.thresholds.memory',
                'ui.theme',
                'api.baseUrl'
            ];
            
            const accessTimes = [];
            const startTime = performance.now();
            
            for (let i = 0; i < this.testConfig.iterations; i++) {
                const configKey = configKeys[i % configKeys.length];
                
                const accessStart = performance.now();
                window.OTA?.getConfig?.(configKey, 'default');
                const accessEnd = performance.now();
                
                const accessTime = accessEnd - accessStart;
                accessTimes.push(accessTime);
                
                results.minAccessTime = Math.min(results.minAccessTime, accessTime);
                results.maxAccessTime = Math.max(results.maxAccessTime, accessTime);
                results.totalAccesses++;
            }
            
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            
            results.averageAccessTime = accessTimes.reduce((sum, time) => sum + time, 0) / accessTimes.length;
            results.accessesPerSecond = (results.totalAccesses / totalTime) * 1000;
            results.passed = results.averageAccessTime < this.testConfig.thresholds.configAccessTime;
            
            console.log(`配置访问测试结果: 平均=${results.averageAccessTime.toFixed(3)}ms, APS=${results.accessesPerSecond.toFixed(0)}`);
            
            this.benchmarkResults.configAccess = results;
        }

        /**
         * 模块加载性能基准测试
         */
        async benchmarkModuleLoading() {
            console.log('📦 测试模块加载性能...');
            
            const results = {
                totalModules: 0,
                loadedModules: 0,
                averageLoadTime: 0,
                totalLoadTime: 0,
                loadingEfficiency: 0,
                passed: false
            };
            
            // 检查已加载的OTA模块
            const otaModules = window.OTA || {};
            const moduleNames = Object.keys(otaModules);
            
            results.totalModules = moduleNames.length;
            results.loadedModules = moduleNames.filter(name => otaModules[name] !== null && otaModules[name] !== undefined).length;
            
            // 模拟模块加载时间测试
            const loadTimes = [];
            
            for (let i = 0; i < 10; i++) {
                const loadStart = performance.now();
                
                // 模拟模块初始化
                const testModule = {
                    name: `testModule${i}`,
                    initialize: function() {
                        // 模拟初始化工作
                        for (let j = 0; j < 1000; j++) {
                            Math.random();
                        }
                    }
                };
                
                testModule.initialize();
                
                const loadEnd = performance.now();
                const loadTime = loadEnd - loadStart;
                loadTimes.push(loadTime);
            }
            
            results.averageLoadTime = loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length;
            results.totalLoadTime = loadTimes.reduce((sum, time) => sum + time, 0);
            results.loadingEfficiency = results.loadedModules / results.totalModules;
            results.passed = results.averageLoadTime < this.testConfig.thresholds.moduleLoadTime;
            
            console.log(`模块加载测试结果: 已加载=${results.loadedModules}/${results.totalModules}, 平均加载时间=${results.averageLoadTime.toFixed(3)}ms`);
            
            this.benchmarkResults.moduleLoading = results;
        }

        /**
         * 事件处理性能基准测试
         */
        async benchmarkEventHandling() {
            console.log('⚡ 测试事件处理性能...');
            
            const results = {
                averageHandlingTime: 0,
                minHandlingTime: Infinity,
                maxHandlingTime: 0,
                totalEvents: 0,
                eventsPerSecond: 0,
                passed: false
            };
            
            const handlingTimes = [];
            const startTime = performance.now();
            
            // 创建测试事件处理器
            const testEventHandler = (event) => {
                // 模拟事件处理工作
                for (let i = 0; i < 100; i++) {
                    Math.random();
                }
            };
            
            // 创建测试元素
            const testElement = document.createElement('div');
            testElement.addEventListener('click', testEventHandler);
            
            for (let i = 0; i < this.testConfig.iterations / 10; i++) {
                const handlingStart = performance.now();
                
                // 触发事件
                const clickEvent = new Event('click');
                testElement.dispatchEvent(clickEvent);
                
                const handlingEnd = performance.now();
                const handlingTime = handlingEnd - handlingStart;
                handlingTimes.push(handlingTime);
                
                results.minHandlingTime = Math.min(results.minHandlingTime, handlingTime);
                results.maxHandlingTime = Math.max(results.maxHandlingTime, handlingTime);
                results.totalEvents++;
            }
            
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            
            results.averageHandlingTime = handlingTimes.reduce((sum, time) => sum + time, 0) / handlingTimes.length;
            results.eventsPerSecond = (results.totalEvents / totalTime) * 1000;
            results.passed = results.averageHandlingTime < this.testConfig.thresholds.eventHandlingTime;
            
            // 清理
            testElement.removeEventListener('click', testEventHandler);
            
            console.log(`事件处理测试结果: 平均=${results.averageHandlingTime.toFixed(3)}ms, EPS=${results.eventsPerSecond.toFixed(0)}`);
            
            this.benchmarkResults.eventHandling = results;
        }

        /**
         * 垃圾回收影响基准测试
         */
        async benchmarkGarbageCollection() {
            console.log('🗑️ 测试垃圾回收影响...');
            
            const results = {
                gcPauses: 0,
                averagePauseTime: 0,
                maxPauseTime: 0,
                memoryReclaimed: 0,
                gcEfficiency: 0,
                passed: false
            };
            
            if (performance.memory) {
                const initialMemory = performance.memory.usedJSHeapSize;
                
                // 创建大量临时对象触发GC
                const tempObjects = [];
                for (let i = 0; i < 50000; i++) {
                    tempObjects.push({
                        id: i,
                        data: new Array(50).fill(Math.random()),
                        nested: {
                            value: Math.random(),
                            array: new Array(20).fill(i)
                        }
                    });
                }
                
                const peakMemory = performance.memory.usedJSHeapSize;
                
                // 清理对象
                tempObjects.length = 0;
                
                // 强制垃圾回收（如果可用）
                if (window.gc) {
                    const gcStart = performance.now();
                    window.gc();
                    const gcEnd = performance.now();
                    
                    results.gcPauses = 1;
                    results.averagePauseTime = gcEnd - gcStart;
                    results.maxPauseTime = gcEnd - gcStart;
                }
                
                // 等待自然垃圾回收
                await this.sleep(200);
                
                const finalMemory = performance.memory.usedJSHeapSize;
                results.memoryReclaimed = peakMemory - finalMemory;
                results.gcEfficiency = results.memoryReclaimed / (peakMemory - initialMemory);
                results.passed = results.averagePauseTime < this.testConfig.thresholds.gcPauseTime;
                
                console.log(`垃圾回收测试结果: 回收内存=${this.formatBytes(results.memoryReclaimed)}, 暂停时间=${results.averagePauseTime.toFixed(3)}ms`);
            }
            
            this.benchmarkResults.garbageCollection = results;
        }

        /**
         * 计算总体性能分数
         */
        calculateOverallPerformance() {
            const scores = [];
            
            // 内存使用分数
            if (this.benchmarkResults.memory.passed) {
                scores.push(100);
            } else {
                const memoryScore = Math.max(0, 100 - (this.benchmarkResults.memory.peakMemory / this.testConfig.thresholds.memoryUsage) * 50);
                scores.push(memoryScore);
            }
            
            // DOM查询分数
            if (this.benchmarkResults.domQueries.passed) {
                scores.push(100);
            } else {
                const domScore = Math.max(0, 100 - (this.benchmarkResults.domQueries.averageQueryTime / this.testConfig.thresholds.domQueryTime) * 50);
                scores.push(domScore);
            }
            
            // 服务访问分数
            if (this.benchmarkResults.serviceAccess.passed) {
                scores.push(100);
            } else {
                const serviceScore = Math.max(0, 100 - (this.benchmarkResults.serviceAccess.averageAccessTime / this.testConfig.thresholds.serviceAccessTime) * 50);
                scores.push(serviceScore);
            }
            
            // 配置访问分数
            if (this.benchmarkResults.configAccess.passed) {
                scores.push(100);
            } else {
                const configScore = Math.max(0, 100 - (this.benchmarkResults.configAccess.averageAccessTime / this.testConfig.thresholds.configAccessTime) * 50);
                scores.push(configScore);
            }
            
            // 模块加载分数
            const moduleScore = this.benchmarkResults.moduleLoading.loadingEfficiency * 100;
            scores.push(moduleScore);
            
            // 事件处理分数
            if (this.benchmarkResults.eventHandling.passed) {
                scores.push(100);
            } else {
                const eventScore = Math.max(0, 100 - (this.benchmarkResults.eventHandling.averageHandlingTime / this.testConfig.thresholds.eventHandlingTime) * 50);
                scores.push(eventScore);
            }
            
            // 垃圾回收分数
            if (this.benchmarkResults.garbageCollection.passed) {
                scores.push(100);
            } else {
                const gcScore = Math.max(0, 100 - (this.benchmarkResults.garbageCollection.averagePauseTime / this.testConfig.thresholds.gcPauseTime) * 50);
                scores.push(gcScore);
            }
            
            // 计算总体分数
            const overallScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
            
            this.benchmarkResults.overall = {
                score: Math.round(overallScore),
                grade: this.getPerformanceGrade(overallScore),
                passedTests: scores.filter(score => score >= 80).length,
                totalTests: scores.length,
                recommendations: this.generatePerformanceRecommendations()
            };
        }

        /**
         * 获取性能等级
         */
        getPerformanceGrade(score) {
            if (score >= 90) return 'A+';
            if (score >= 80) return 'A';
            if (score >= 70) return 'B';
            if (score >= 60) return 'C';
            if (score >= 50) return 'D';
            return 'F';
        }

        /**
         * 生成性能改进建议
         */
        generatePerformanceRecommendations() {
            const recommendations = [];
            
            if (!this.benchmarkResults.memory.passed) {
                recommendations.push('优化内存使用：减少大对象创建，及时清理不用的引用');
            }
            
            if (!this.benchmarkResults.domQueries.passed) {
                recommendations.push('优化DOM查询：使用更具体的选择器，缓存查询结果');
            }
            
            if (!this.benchmarkResults.serviceAccess.passed) {
                recommendations.push('优化服务访问：实现服务缓存，减少重复查找');
            }
            
            if (!this.benchmarkResults.configAccess.passed) {
                recommendations.push('优化配置访问：实现配置缓存，预加载常用配置');
            }
            
            if (this.benchmarkResults.moduleLoading.loadingEfficiency < 0.8) {
                recommendations.push('优化模块加载：实现懒加载，减少初始化时间');
            }
            
            if (!this.benchmarkResults.eventHandling.passed) {
                recommendations.push('优化事件处理：使用事件委托，减少处理器数量');
            }
            
            if (!this.benchmarkResults.garbageCollection.passed) {
                recommendations.push('优化垃圾回收：减少临时对象创建，使用对象池');
            }
            
            return recommendations;
        }

        /**
         * 生成性能报告
         */
        generatePerformanceReport() {
            return {
                timestamp: new Date().toISOString(),
                overall: this.benchmarkResults.overall,
                detailed: {
                    memory: this.benchmarkResults.memory,
                    domQueries: this.benchmarkResults.domQueries,
                    serviceAccess: this.benchmarkResults.serviceAccess,
                    configAccess: this.benchmarkResults.configAccess,
                    moduleLoading: this.benchmarkResults.moduleLoading,
                    eventHandling: this.benchmarkResults.eventHandling,
                    garbageCollection: this.benchmarkResults.garbageCollection
                },
                thresholds: this.testConfig.thresholds,
                recommendations: this.benchmarkResults.overall.recommendations
            };
        }

        /**
         * 启动内存监控
         */
        startMemoryMonitoring() {
            if (performance.memory) {
                this.memoryMonitor = setInterval(() => {
                    const memoryUsage = performance.memory.usedJSHeapSize;
                    if (memoryUsage > this.testConfig.thresholds.memoryUsage) {
                        console.warn(`⚠️ 内存使用过高: ${this.formatBytes(memoryUsage)}`);
                    }
                }, this.testConfig.memoryMeasurementInterval);
            }
        }

        /**
         * 停止内存监控
         */
        stopMemoryMonitoring() {
            if (this.memoryMonitor) {
                clearInterval(this.memoryMonitor);
                this.memoryMonitor = null;
            }
        }

        /**
         * 处理性能条目
         */
        handlePerformanceEntries(entries) {
            entries.forEach(entry => {
                if (entry.name.startsWith(this.testConfig.performanceMarkPrefix)) {
                    console.log(`📊 性能标记: ${entry.name} - ${entry.duration}ms`);
                }
            });
        }

        /**
         * 格式化字节数
         */
        formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        /**
         * 睡眠函数
         */
        sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取基准测试状态
         */
        getBenchmarkStatus() {
            return {
                isRunning: false,
                results: this.benchmarkResults,
                config: this.testConfig
            };
        }

        /**
         * 清理资源
         */
        cleanup() {
            this.stopMemoryMonitoring();
            
            if (this.performanceObserver) {
                this.performanceObserver.disconnect();
                this.performanceObserver = null;
            }
        }
    }

    // 创建全局唯一的性能基准测试实例
    const performanceBenchmarkTest = new PerformanceBenchmarkTest();

    // 暴露到OTA命名空间
    window.OTA.tests.performanceBenchmarkTest = performanceBenchmarkTest;

    // 提供全局命令
    window.runPerformanceBenchmark = async () => {
        console.group('⚡ 性能基准测试');
        const report = await performanceBenchmarkTest.runAllBenchmarks();
        console.log('📊 性能报告:', report);
        console.groupEnd();
        return report;
    };

    window.getPerformanceBenchmarkStatus = () => {
        return performanceBenchmarkTest.getBenchmarkStatus();
    };

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', () => {
        performanceBenchmarkTest.cleanup();
    });

    console.log('✅ 性能基准测试模块已加载');

})();
