/**
 * @TEST_SUITE 综合测试套件
 * 🏷️ 标签: @COMPREHENSIVE_TEST @PERFORMANCE_TEST @FUNCTIONALITY_TEST
 * 📝 说明: 全面测试系统功能、性能和集成
 * 🎯 目标: 确保系统稳定性、性能达标、功能完整
 */

(function() {
    'use strict';
    
    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.gemini = window.OTA.gemini || {};
    window.OTA.gemini.tests = window.OTA.gemini.tests || {};
    
    /**
     * 综合测试套件类
     */
    class ComprehensiveTestSuite {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.testResults = [];
            this.performanceMetrics = {};
            
            // 测试配置
            this.config = {
                timeout: 30000, // 30秒超时
                performanceThresholds: {
                    responseTime: 2000, // 2秒响应时间
                    memoryUsage: 100 * 1024 * 1024, // 100MB内存
                    cacheHitRate: 0.7, // 70%缓存命中率
                    errorRate: 0.05 // 5%错误率
                },
                testData: {
                    sampleOrders: [
                        '接机服务\n客户：张三\n电话：13800138000\n航班：CA1234\n时间：2024-01-15 10:30',
                        '送机服务\n客户：李四\n电话：13900139000\n酒店：香格里拉酒店\n时间：2024-01-16 08:00',
                        '包车服务\n客户：王五\n电话：13700137000\n行程：机场-酒店-景点\n时间：2024-01-17 全天'
                    ],
                    multipleOrders: `订单1：接机服务
客户：张三，电话：13800138000
航班：CA1234，时间：2024-01-15 10:30

订单2：送机服务  
客户：李四，电话：13900139000
酒店：香格里拉酒店，时间：2024-01-16 08:00

订单3：包车服务
客户：王五，电话：13700137000
行程：机场-酒店-景点，时间：2024-01-17 全天`
                }
            };
        }
        
        /**
         * 运行所有测试
         * @returns {Promise<Object>} 测试结果
         */
        async runAllTests() {
            this.logger.log('开始综合测试套件', 'info');
            this.testResults = [];
            this.performanceMetrics = {};
            
            const testSuites = [
                { name: '系统初始化测试', method: this.testSystemInitialization },
                { name: '服务可用性测试', method: this.testServiceAvailability },
                { name: '配置完整性测试', method: this.testConfigurationIntegrity },
                { name: 'OTA处理器测试', method: this.testOTAProcessors },
                { name: '语言集成测试', method: this.testLanguageIntegration },
                { name: '字段映射测试', method: this.testFieldMapping },
                { name: '预设值测试', method: this.testPresetValues },
                { name: '降级处理测试', method: this.testFallbackHandling },
                { name: '性能监控测试', method: this.testPerformanceMonitoring },
                { name: '错误处理测试', method: this.testErrorHandling },
                { name: '集成兼容性测试', method: this.testIntegrationCompatibility },
                { name: '性能基准测试', method: this.testPerformanceBenchmarks },
                { name: '压力测试', method: this.testStressLoad },
                { name: '端到端功能测试', method: this.testEndToEndFunctionality }
            ];
            
            for (const suite of testSuites) {
                try {
                    const result = await this.runTestSuite(suite.name, suite.method.bind(this));
                    this.testResults.push(result);
                } catch (error) {
                    this.testResults.push({
                        name: suite.name,
                        passed: false,
                        error: error.message,
                        timestamp: new Date().toISOString()
                    });
                }
            }
            
            const summary = this.generateTestSummary();
            const report = this.generateDetailedReport();
            
            this.logger.log('综合测试套件完成', 'info', summary);
            
            return {
                summary,
                report,
                results: this.testResults,
                performanceMetrics: this.performanceMetrics,
                passed: summary.passed === summary.total
            };
        }
        
        /**
         * 运行单个测试套件
         * @param {string} suiteName - 测试套件名称
         * @param {Function} testMethod - 测试方法
         * @returns {Promise<Object>} 测试结果
         */
        async runTestSuite(suiteName, testMethod) {
            const startTime = Date.now();
            
            try {
                this.logger.log(`开始测试: ${suiteName}`, 'info');
                
                const result = await Promise.race([
                    testMethod(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('测试超时')), this.config.timeout)
                    )
                ]);
                
                const duration = Date.now() - startTime;
                
                this.logger.log(`✅ ${suiteName} 通过 (${duration}ms)`, 'success');
                
                return {
                    name: suiteName,
                    passed: true,
                    duration,
                    result,
                    timestamp: new Date().toISOString()
                };
                
            } catch (error) {
                const duration = Date.now() - startTime;
                
                this.logger.logError(`❌ ${suiteName} 失败 (${duration}ms)`, error);
                
                return {
                    name: suiteName,
                    passed: false,
                    duration,
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            }
        }
        
        /**
         * 测试系统初始化
         */
        async testSystemInitialization() {
            const checks = [];
            
            // 检查OTA命名空间
            checks.push({
                name: 'OTA命名空间存在',
                condition: !!window.OTA,
                value: !!window.OTA
            });
            
            // 检查Gemini命名空间
            checks.push({
                name: 'Gemini命名空间存在',
                condition: !!(window.OTA?.gemini),
                value: !!(window.OTA?.gemini)
            });
            
            // 检查核心服务
            const coreServices = ['logger', 'appState', 'apiService', 'geminiService', 'uiManager'];
            for (const service of coreServices) {
                const serviceExists = !!(window.OTA?.[service] || window[service]);
                checks.push({
                    name: `${service}服务存在`,
                    condition: serviceExists,
                    value: serviceExists
                });
            }
            
            const failedChecks = checks.filter(check => !check.condition);
            if (failedChecks.length > 0) {
                throw new Error(`系统初始化检查失败: ${failedChecks.map(c => c.name).join(', ')}`);
            }
            
            return { checks, allPassed: true };
        }
        
        /**
         * 测试服务可用性
         */
        async testServiceAvailability() {
            const services = [
                { name: 'GeminiService', getter: () => window.getGeminiService?.() },
                { name: 'GeminiCoordinator', getter: () => window.getGeminiCoordinator?.() },
                { name: 'APIService', getter: () => window.getAPIService?.() },
                { name: 'Logger', getter: () => window.getLogger?.() },
                { name: 'AppState', getter: () => window.getAppState?.() }
            ];
            
            const results = {};
            
            for (const service of services) {
                try {
                    const instance = service.getter();
                    results[service.name] = {
                        available: !!instance,
                        type: typeof instance,
                        hasRequiredMethods: this.checkRequiredMethods(service.name, instance)
                    };
                } catch (error) {
                    results[service.name] = {
                        available: false,
                        error: error.message
                    };
                }
            }
            
            const unavailableServices = Object.entries(results)
                .filter(([name, result]) => !result.available)
                .map(([name]) => name);
                
            if (unavailableServices.length > 0) {
                throw new Error(`服务不可用: ${unavailableServices.join(', ')}`);
            }
            
            return results;
        }
        
        /**
         * 检查服务必需方法
         * @param {string} serviceName - 服务名称
         * @param {Object} instance - 服务实例
         * @returns {boolean} 是否有必需方法
         */
        checkRequiredMethods(serviceName, instance) {
            if (!instance) return false;
            
            const requiredMethods = {
                'GeminiService': ['parseOrder', 'isAvailable'],
                'GeminiCoordinator': ['processOrder', 'initialize'],
                'APIService': ['getAllSystemData', 'createOrder'],
                'Logger': ['log', 'logError'],
                'AppState': ['get', 'set']
            };
            
            const methods = requiredMethods[serviceName] || [];
            return methods.every(method => typeof instance[method] === 'function');
        }
        
        /**
         * 测试配置完整性
         */
        async testConfigurationIntegrity() {
            const configs = [
                { name: 'FieldMapping', path: 'window.OTA.gemini.configs.fieldMappingConfig' },
                { name: 'PresetValues', path: 'window.OTA.gemini.configs.presetValuesConfig' },
                { name: 'FallbackConfig', path: 'window.OTA.gemini.configs.fallbackConfig' },
                { name: 'PerformanceOptimization', path: 'window.OTA.gemini.configs.performanceOptimization' }
            ];
            
            const results = {};
            
            for (const config of configs) {
                try {
                    const configInstance = this.getNestedProperty(window, config.path.split('.').slice(1));
                    results[config.name] = {
                        exists: !!configInstance,
                        type: typeof configInstance,
                        hasConfig: !!(configInstance?.config || configInstance?.getConfig)
                    };
                } catch (error) {
                    results[config.name] = {
                        exists: false,
                        error: error.message
                    };
                }
            }
            
            const missingConfigs = Object.entries(results)
                .filter(([name, result]) => !result.exists)
                .map(([name]) => name);
                
            if (missingConfigs.length > 0) {
                throw new Error(`配置缺失: ${missingConfigs.join(', ')}`);
            }
            
            return results;
        }
        
        /**
         * 测试OTA处理器
         */
        async testOTAProcessors() {
            const processors = [
                'chong-dealer-processor',
                'klook-processor', 
                'ctrip-processor',
                'generic-processor'
            ];
            
            const results = {};
            
            for (const processor of processors) {
                try {
                    const processorPath = `window.OTA.gemini.processors.${processor.replace(/-/g, '')}`;
                    const processorInstance = this.getNestedProperty(window, processorPath.split('.').slice(1));
                    
                    results[processor] = {
                        exists: !!processorInstance,
                        type: typeof processorInstance,
                        hasProcessMethod: !!(processorInstance?.process || processorInstance?.processOrder)
                    };
                } catch (error) {
                    results[processor] = {
                        exists: false,
                        error: error.message
                    };
                }
            }
            
            return results;
        }
        
        /**
         * 测试语言集成
         */
        async testLanguageIntegration() {
            const languageIntegration = window.OTA?.gemini?.core?.languageOtaIntegration;
            
            if (!languageIntegration) {
                throw new Error('语言集成模块不存在');
            }
            
            // 测试语言检测
            const testTexts = [
                { text: '您好，我需要接机服务', expectedLanguage: 'chinese' },
                { text: 'Hello, I need airport pickup', expectedLanguage: 'english' },
                { text: 'こんにちは、空港送迎が必要です', expectedLanguage: 'japanese' }
            ];
            
            const results = {};
            
            for (const test of testTexts) {
                try {
                    // 注意：这里只测试方法存在性，不实际调用可能需要API的方法
                    const hasDetectionMethod = typeof languageIntegration.detectLanguage === 'function' ||
                                             typeof languageIntegration.analyzeLanguageContext === 'function';
                    
                    results[test.expectedLanguage] = {
                        hasDetectionMethod,
                        testText: test.text.substring(0, 20) + '...'
                    };
                } catch (error) {
                    results[test.expectedLanguage] = {
                        error: error.message
                    };
                }
            }
            
            return results;
        }
        
        /**
         * 测试字段映射
         */
        async testFieldMapping() {
            const fieldMapping = window.OTA?.gemini?.configs?.fieldMappingConfig;
            
            if (!fieldMapping) {
                throw new Error('字段映射配置不存在');
            }
            
            // 测试基本映射功能
            const testMappings = [
                { from: 'pickup_location', to: 'pickup' },
                { from: 'dropoff_location', to: 'destination' },
                { from: 'pickup_date', to: 'date' },
                { from: 'pickup_time', to: 'time' }
            ];
            
            const results = {};
            
            for (const mapping of testMappings) {
                try {
                    const hasMapping = fieldMapping.commonFieldMapping && 
                                     fieldMapping.commonFieldMapping[mapping.from] === mapping.to;
                    
                    results[mapping.from] = {
                        hasMapping,
                        expectedTarget: mapping.to,
                        actualTarget: fieldMapping.commonFieldMapping?.[mapping.from]
                    };
                } catch (error) {
                    results[mapping.from] = {
                        error: error.message
                    };
                }
            }
            
            return results;
        }
        
        /**
         * 测试预设值
         */
        async testPresetValues() {
            const presetValues = window.OTA?.gemini?.configs?.presetValuesConfig;
            
            if (!presetValues) {
                throw new Error('预设值配置不存在');
            }
            
            // 测试预设值功能
            const testPresets = [
                { field: 'car_type_id', expectedDefault: 1 },
                { field: 'languages_id', expectedDefault: [2] },
                { field: 'service_type_id', expectedType: 'number' }
            ];
            
            const results = {};
            
            for (const preset of testPresets) {
                try {
                    const hasPreset = presetValues.commonPresets && 
                                    preset.field in presetValues.commonPresets;
                    
                    results[preset.field] = {
                        hasPreset,
                        value: presetValues.commonPresets?.[preset.field]
                    };
                } catch (error) {
                    results[preset.field] = {
                        error: error.message
                    };
                }
            }
            
            return results;
        }
        
        /**
         * 测试降级处理
         */
        async testFallbackHandling() {
            const fallbackConfig = window.OTA?.gemini?.configs?.fallbackConfig;
            
            if (!fallbackConfig) {
                throw new Error('降级处理配置不存在');
            }
            
            // 测试降级策略
            const testStrategies = [
                'otaIdentification',
                'referenceExtraction', 
                'fieldMapping',
                'dataValidation'
            ];
            
            const results = {};
            
            for (const strategy of testStrategies) {
                try {
                    const hasStrategy = fallbackConfig.fallbackStrategies && 
                                      strategy in fallbackConfig.fallbackStrategies;
                    
                    results[strategy] = {
                        hasStrategy,
                        config: fallbackConfig.fallbackStrategies?.[strategy]
                    };
                } catch (error) {
                    results[strategy] = {
                        error: error.message
                    };
                }
            }
            
            return results;
        }
        
        /**
         * 测试性能监控
         */
        async testPerformanceMonitoring() {
            const performanceMonitor = window.OTA?.gemini?.core?.performanceMonitor;
            
            if (!performanceMonitor) {
                throw new Error('性能监控模块不存在');
            }
            
            // 测试性能监控功能
            const testMetrics = [
                'responseTimes',
                'memoryUsage',
                'apiCalls',
                'errorRates'
            ];
            
            const results = {};
            
            for (const metric of testMetrics) {
                try {
                    const hasMetric = performanceMonitor.metrics && 
                                    metric in performanceMonitor.metrics;
                    
                    results[metric] = {
                        hasMetric,
                        type: typeof performanceMonitor.metrics?.[metric]
                    };
                } catch (error) {
                    results[metric] = {
                        error: error.message
                    };
                }
            }
            
            return results;
        }
        
        /**
         * 测试错误处理
         */
        async testErrorHandling() {
            const errorHandler = window.OTA?.gemini?.core?.errorHandler;
            
            if (!errorHandler) {
                throw new Error('错误处理模块不存在');
            }
            
            // 测试错误处理功能
            const testCategories = [
                'SYSTEM',
                'NETWORK', 
                'API',
                'VALIDATION'
            ];
            
            const results = {};
            
            for (const category of testCategories) {
                try {
                    const hasCategory = errorHandler.errorCategories && 
                                      category in errorHandler.errorCategories;
                    
                    results[category] = {
                        hasCategory,
                        value: errorHandler.errorCategories?.[category]
                    };
                } catch (error) {
                    results[category] = {
                        error: error.message
                    };
                }
            }
            
            return results;
        }
        
        /**
         * 测试集成兼容性
         */
        async testIntegrationCompatibility() {
            // 运行系统集成测试
            const integrationTest = window.OTA?.gemini?.tests?.systemIntegrationTest;
            
            if (!integrationTest) {
                throw new Error('系统集成测试模块不存在');
            }
            
            try {
                const integrationResults = await integrationTest.runAllTests();
                return {
                    integrationTestPassed: integrationResults.passed,
                    summary: integrationResults.summary,
                    failedTests: integrationResults.results.filter(r => !r.passed)
                };
            } catch (error) {
                throw new Error(`集成兼容性测试失败: ${error.message}`);
            }
        }
        
        /**
         * 测试性能基准
         */
        async testPerformanceBenchmarks() {
            const startTime = Date.now();
            const initialMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            // 模拟性能测试
            const testOperations = [
                () => this.simulateOrderParsing(),
                () => this.simulateOTAIdentification(),
                () => this.simulateLanguageDetection(),
                () => this.simulateFieldMapping()
            ];
            
            const operationResults = [];
            
            for (const operation of testOperations) {
                const opStartTime = Date.now();
                try {
                    await operation();
                    const opDuration = Date.now() - opStartTime;
                    operationResults.push({
                        operation: operation.name,
                        duration: opDuration,
                        success: true
                    });
                } catch (error) {
                    operationResults.push({
                        operation: operation.name,
                        duration: Date.now() - opStartTime,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            const totalDuration = Date.now() - startTime;
            const finalMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            const memoryDelta = finalMemory - initialMemory;
            
            // 检查性能阈值
            const performanceIssues = [];
            
            if (totalDuration > this.config.performanceThresholds.responseTime) {
                performanceIssues.push(`总响应时间超过阈值: ${totalDuration}ms > ${this.config.performanceThresholds.responseTime}ms`);
            }
            
            if (memoryDelta > this.config.performanceThresholds.memoryUsage) {
                performanceIssues.push(`内存使用超过阈值: ${memoryDelta} bytes > ${this.config.performanceThresholds.memoryUsage} bytes`);
            }
            
            this.performanceMetrics = {
                totalDuration,
                memoryDelta,
                operationResults,
                performanceIssues
            };
            
            if (performanceIssues.length > 0) {
                throw new Error(`性能基准测试失败: ${performanceIssues.join(', ')}`);
            }
            
            return this.performanceMetrics;
        }
        
        /**
         * 测试压力负载
         */
        async testStressLoad() {
            const concurrentRequests = 10;
            const requestsPerBatch = 5;
            
            const stressTestPromises = [];
            
            for (let i = 0; i < concurrentRequests; i++) {
                stressTestPromises.push(this.simulateStressRequest(i));
            }
            
            try {
                const results = await Promise.all(stressTestPromises);
                const successCount = results.filter(r => r.success).length;
                const errorRate = (concurrentRequests - successCount) / concurrentRequests;
                
                if (errorRate > this.config.performanceThresholds.errorRate) {
                    throw new Error(`压力测试错误率过高: ${(errorRate * 100).toFixed(2)}% > ${(this.config.performanceThresholds.errorRate * 100).toFixed(2)}%`);
                }
                
                return {
                    totalRequests: concurrentRequests,
                    successCount,
                    errorRate: errorRate * 100,
                    results
                };
            } catch (error) {
                throw new Error(`压力测试失败: ${error.message}`);
            }
        }
        
        /**
         * 测试端到端功能
         */
        async testEndToEndFunctionality() {
            // 测试完整的订单处理流程
            const testOrder = this.config.testData.sampleOrders[0];
            
            try {
                // 1. 检查Gemini服务可用性
                const geminiService = window.getGeminiService();
                if (!geminiService || !geminiService.isAvailable()) {
                    throw new Error('Gemini服务不可用');
                }
                
                // 2. 检查解析方法存在
                if (typeof geminiService.parseOrder !== 'function') {
                    throw new Error('parseOrder方法不存在');
                }
                
                // 3. 检查多订单管理器
                const multiOrderManager = window.OTA?.multiOrderManager;
                if (!multiOrderManager) {
                    throw new Error('多订单管理器不存在');
                }
                
                // 4. 检查表单管理器
                const formManager = window.OTA?.managers?.FormManager;
                if (!formManager) {
                    throw new Error('表单管理器不存在');
                }
                
                return {
                    geminiServiceReady: true,
                    multiOrderManagerReady: true,
                    formManagerReady: true,
                    endToEndReady: true
                };
                
            } catch (error) {
                throw new Error(`端到端功能测试失败: ${error.message}`);
            }
        }
        
        /**
         * 模拟订单解析
         * @private
         */
        async simulateOrderParsing() {
            // 模拟解析操作
            await new Promise(resolve => setTimeout(resolve, 100));
            return { operation: 'orderParsing', success: true };
        }
        
        /**
         * 模拟OTA识别
         * @private
         */
        async simulateOTAIdentification() {
            // 模拟OTA识别操作
            await new Promise(resolve => setTimeout(resolve, 50));
            return { operation: 'otaIdentification', success: true };
        }
        
        /**
         * 模拟语言检测
         * @private
         */
        async simulateLanguageDetection() {
            // 模拟语言检测操作
            await new Promise(resolve => setTimeout(resolve, 30));
            return { operation: 'languageDetection', success: true };
        }
        
        /**
         * 模拟字段映射
         * @private
         */
        async simulateFieldMapping() {
            // 模拟字段映射操作
            await new Promise(resolve => setTimeout(resolve, 20));
            return { operation: 'fieldMapping', success: true };
        }
        
        /**
         * 模拟压力请求
         * @param {number} requestId - 请求ID
         * @returns {Promise<Object>} 请求结果
         * @private
         */
        async simulateStressRequest(requestId) {
            try {
                // 模拟随机延迟
                const delay = Math.random() * 200 + 50;
                await new Promise(resolve => setTimeout(resolve, delay));
                
                // 模拟随机失败
                if (Math.random() < 0.1) { // 10%失败率
                    throw new Error(`模拟请求失败 ${requestId}`);
                }
                
                return {
                    requestId,
                    success: true,
                    duration: delay
                };
            } catch (error) {
                return {
                    requestId,
                    success: false,
                    error: error.message
                };
            }
        }
        
        /**
         * 获取嵌套属性
         * @param {Object} obj - 对象
         * @param {Array} path - 属性路径
         * @returns {*} 属性值
         * @private
         */
        getNestedProperty(obj, path) {
            return path.reduce((current, key) => current && current[key], obj);
        }
        
        /**
         * 生成测试摘要
         * @returns {Object} 测试摘要
         */
        generateTestSummary() {
            const total = this.testResults.length;
            const passed = this.testResults.filter(r => r.passed).length;
            const failed = total - passed;
            const totalDuration = this.testResults.reduce((sum, r) => sum + (r.duration || 0), 0);
            
            return {
                total,
                passed,
                failed,
                passRate: total > 0 ? (passed / total * 100).toFixed(2) + '%' : '0%',
                totalDuration: totalDuration + 'ms',
                timestamp: new Date().toISOString()
            };
        }
        
        /**
         * 生成详细报告
         * @returns {Object} 详细报告
         */
        generateDetailedReport() {
            const failedTests = this.testResults.filter(r => !r.passed);
            const performanceIssues = this.performanceMetrics.performanceIssues || [];
            
            return {
                summary: this.generateTestSummary(),
                failedTests,
                performanceMetrics: this.performanceMetrics,
                performanceIssues,
                recommendations: this.generateRecommendations(),
                environment: {
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString(),
                    memoryInfo: performance.memory ? {
                        usedJSHeapSize: performance.memory.usedJSHeapSize,
                        totalJSHeapSize: performance.memory.totalJSHeapSize,
                        jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                    } : null
                }
            };
        }
        
        /**
         * 生成优化建议
         * @returns {Array} 建议列表
         */
        generateRecommendations() {
            const recommendations = [];
            
            const failedTests = this.testResults.filter(r => !r.passed);
            if (failedTests.length > 0) {
                recommendations.push({
                    type: 'functionality',
                    priority: 'high',
                    message: `有 ${failedTests.length} 个功能测试失败，需要修复`
                });
            }
            
            if (this.performanceMetrics.performanceIssues?.length > 0) {
                recommendations.push({
                    type: 'performance',
                    priority: 'medium',
                    message: '存在性能问题，建议优化'
                });
            }
            
            const passRate = this.testResults.length > 0 ? 
                this.testResults.filter(r => r.passed).length / this.testResults.length : 0;
                
            if (passRate < 0.9) {
                recommendations.push({
                    type: 'quality',
                    priority: 'high',
                    message: `测试通过率较低 (${(passRate * 100).toFixed(2)}%)，需要提升系统稳定性`
                });
            }
            
            return recommendations;
        }
    }
    
    // 创建全局实例
    const comprehensiveTestSuite = new ComprehensiveTestSuite();
    
    // 注册到全局命名空间
    window.OTA.gemini.tests.ComprehensiveTestSuite = ComprehensiveTestSuite;
    window.OTA.gemini.tests.comprehensiveTestSuite = comprehensiveTestSuite;
    
    // 便捷访问函数
    window.OTA.gemini.tests.runComprehensiveTests = function() {
        return comprehensiveTestSuite.runAllTests();
    };
    
    window.OTA.gemini.tests.getTestReport = function() {
        return comprehensiveTestSuite.generateDetailedReport();
    };
    
    console.log('✅ 综合测试套件已加载');
    
})();
