/**
 * Gemini AI 核心功能模块
 * 合并原 js/ai/gemini/core/ 目录下的核心功能
 * <AUTHOR> System
 * @created 2025-01-29
 */

window.OTA = window.OTA || {};
window.OTA.ai = window.OTA.ai || {};
window.OTA.ai.gemini = window.OTA.ai.gemini || {};

(function() {
    'use strict';

    // 获取依赖服务的函数
    function getLogger() {
        return window.OTA?.services?.logger || console;
    }

    /**
     * 航班号处理器
     * 原文件：js/ai/gemini/core/flight-number-processor.js
     */
    class FlightNumberProcessor {
        constructor() {
            this.logger = getLogger();
        }

        /**
         * 处理航班号信息
         * @param {string} text - 包含航班号的文本
         * @returns {Object} 处理结果
         */
        process(text) {
            try {
                // 航班号正则表达式
                const flightPatterns = [
                    /([A-Z]{2,3})\s*(\d{3,4})/g,  // 标准航班号格式
                    /航班[：:]?\s*([A-Z]{2,3})\s*(\d{3,4})/g,  // 中文航班号
                    /Flight[：:]?\s*([A-Z]{2,3})\s*(\d{3,4})/gi  // 英文航班号
                ];

                const flights = [];
                flightPatterns.forEach(pattern => {
                    let match;
                    while ((match = pattern.exec(text)) !== null) {
                        flights.push({
                            airline: match[1],
                            number: match[2],
                            full: `${match[1]}${match[2]}`
                        });
                    }
                });

                return {
                    success: true,
                    flights: flights,
                    count: flights.length
                };
            } catch (error) {
                this.logger.error('航班号处理失败:', error);
                return {
                    success: false,
                    error: error.message,
                    flights: []
                };
            }
        }
    }

    /**
     * 数据标准化器
     * 原文件：js/ai/gemini/core/data-normalizer.js
     */
    class DataNormalizer {
        constructor() {
            this.logger = getLogger();
        }

        /**
         * 标准化订单数据
         * @param {Object} data - 原始数据
         * @returns {Object} 标准化后的数据
         */
        normalize(data) {
            try {
                const normalized = {};

                // 标准化日期格式
                if (data.pickup_date) {
                    normalized.pickup_date = this.normalizeDate(data.pickup_date);
                }

                // 标准化时间格式
                if (data.pickup_time) {
                    normalized.pickup_time = this.normalizeTime(data.pickup_time);
                }

                // 标准化地址
                if (data.pickup_location) {
                    normalized.pickup_location = this.normalizeAddress(data.pickup_location);
                }
                if (data.dropoff_location) {
                    normalized.dropoff_location = this.normalizeAddress(data.dropoff_location);
                }

                // 标准化价格
                if (data.price) {
                    normalized.price = this.normalizePrice(data.price);
                }

                // 标准化乘客信息
                if (data.passenger_count) {
                    normalized.passenger_count = this.normalizePassengerCount(data.passenger_count);
                }

                return {
                    success: true,
                    data: { ...data, ...normalized }
                };
            } catch (error) {
                this.logger.error('数据标准化失败:', error);
                return {
                    success: false,
                    error: error.message,
                    data: data
                };
            }
        }

        /**
         * 标准化日期格式为 DD-MM-YYYY
         */
        normalizeDate(dateStr) {
            try {
                // 处理各种日期格式
                const datePatterns = [
                    /(\d{4})-(\d{1,2})-(\d{1,2})/,  // YYYY-MM-DD
                    /(\d{1,2})\/(\d{1,2})\/(\d{4})/,  // MM/DD/YYYY
                    /(\d{1,2})-(\d{1,2})-(\d{4})/,   // DD-MM-YYYY
                ];

                for (const pattern of datePatterns) {
                    const match = dateStr.match(pattern);
                    if (match) {
                        const [, part1, part2, part3] = match;
                        // 根据模式转换为 DD-MM-YYYY
                        if (pattern.source.includes('\\d{4}')) {
                            // YYYY-MM-DD 格式
                            return `${part3.padStart(2, '0')}-${part2.padStart(2, '0')}-${part1}`;
                        } else {
                            // DD-MM-YYYY 或 MM/DD/YYYY 格式
                            return `${part1.padStart(2, '0')}-${part2.padStart(2, '0')}-${part3}`;
                        }
                    }
                }
                return dateStr;
            } catch (error) {
                this.logger.warn('日期标准化失败:', error);
                return dateStr;
            }
        }

        /**
         * 标准化时间格式为 HH:MM
         */
        normalizeTime(timeStr) {
            try {
                const timePattern = /(\d{1,2})[：:](\d{2})/;
                const match = timeStr.match(timePattern);
                if (match) {
                    const [, hours, minutes] = match;
                    return `${hours.padStart(2, '0')}:${minutes}`;
                }
                return timeStr;
            } catch (error) {
                this.logger.warn('时间标准化失败:', error);
                return timeStr;
            }
        }

        /**
         * 标准化地址
         */
        normalizeAddress(address) {
            try {
                // 移除多余空格
                let normalized = address.trim().replace(/\s+/g, ' ');
                
                // 标准化常见地址缩写
                const addressMappings = {
                    'KLIA': 'Kuala Lumpur International Airport',
                    'KLIA2': 'Kuala Lumpur International Airport 2',
                    'KL': 'Kuala Lumpur',
                    'PJ': 'Petaling Jaya'
                };

                Object.entries(addressMappings).forEach(([abbr, full]) => {
                    const regex = new RegExp(`\\b${abbr}\\b`, 'gi');
                    normalized = normalized.replace(regex, full);
                });

                return normalized;
            } catch (error) {
                this.logger.warn('地址标准化失败:', error);
                return address;
            }
        }

        /**
         * 标准化价格
         */
        normalizePrice(price) {
            try {
                // 提取数字
                const numericPrice = parseFloat(price.toString().replace(/[^\d.]/g, ''));
                return isNaN(numericPrice) ? 0 : numericPrice;
            } catch (error) {
                this.logger.warn('价格标准化失败:', error);
                return 0;
            }
        }

        /**
         * 标准化乘客数量
         */
        normalizePassengerCount(count) {
            try {
                const numericCount = parseInt(count.toString().replace(/[^\d]/g, ''));
                return isNaN(numericCount) ? 1 : Math.max(1, numericCount);
            } catch (error) {
                this.logger.warn('乘客数量标准化失败:', error);
                return 1;
            }
        }
    }

    /**
     * OTA参考引擎
     * 原文件：js/ai/gemini/core/ota-reference-engine.js
     */
    class OTAReferenceEngine {
        constructor() {
            this.logger = getLogger();
        }

        /**
         * 生成OTA参考号
         * @param {Object} orderData - 订单数据
         * @returns {string} OTA参考号
         */
        generateReference(orderData) {
            try {
                const timestamp = Date.now().toString().slice(-6);
                const randomSuffix = Math.random().toString(36).substring(2, 5).toUpperCase();
                
                // 根据OTA类型生成不同格式的参考号
                const otaType = orderData.ota_type || 'GENERAL';
                
                switch (otaType.toLowerCase()) {
                    case 'chong-dealer':
                        return `CD${timestamp}${randomSuffix}`;
                    case 'agoda':
                        return `AG${timestamp}${randomSuffix}`;
                    case 'booking':
                        return `BK${timestamp}${randomSuffix}`;
                    default:
                        return `OTA${timestamp}${randomSuffix}`;
                }
            } catch (error) {
                this.logger.error('OTA参考号生成失败:', error);
                return `OTA${Date.now().toString().slice(-6)}`;
            }
        }
    }

    /**
     * 地址翻译器
     * 原文件：js/ai/gemini/core/address-translator.js
     */
    class AddressTranslator {
        constructor() {
            this.logger = getLogger();
            this.translations = {
                // 马来西亚常见地址翻译
                'Kuala Lumpur': '吉隆坡',
                'Petaling Jaya': '八打灵再也',
                'Shah Alam': '莎阿南',
                'Subang Jaya': '梳邦再也',
                'Klang': '巴生',
                'Selangor': '雪兰莪',
                'KLIA': '吉隆坡国际机场',
                'KLIA2': '吉隆坡第二国际机场'
            };
        }

        /**
         * 翻译地址
         * @param {string} address - 原始地址
         * @param {string} targetLang - 目标语言 ('zh'|'en')
         * @returns {string} 翻译后的地址
         */
        translate(address, targetLang = 'zh') {
            try {
                if (targetLang === 'zh') {
                    // 英文到中文
                    let translated = address;
                    Object.entries(this.translations).forEach(([en, zh]) => {
                        const regex = new RegExp(en, 'gi');
                        translated = translated.replace(regex, zh);
                    });
                    return translated;
                } else {
                    // 中文到英文
                    let translated = address;
                    Object.entries(this.translations).forEach(([en, zh]) => {
                        const regex = new RegExp(zh, 'g');
                        translated = translated.replace(regex, en);
                    });
                    return translated;
                }
            } catch (error) {
                this.logger.warn('地址翻译失败:', error);
                return address;
            }
        }
    }

    /**
     * 提示模板引擎
     * 原文件：js/ai/gemini/core/prompt-template-engine.js
     */
    class PromptTemplateEngine {
        constructor() {
            this.logger = getLogger();
            this.templates = {
                orderParsing: `请解析以下订单信息，提取关键字段：

订单内容：
{orderText}

请返回JSON格式，包含以下字段：
- pickup_date: 接送日期 (DD-MM-YYYY格式)
- pickup_time: 接送时间 (HH:MM格式)
- pickup_location: 接送地点
- dropoff_location: 目的地
- passenger_count: 乘客人数
- luggage_count: 行李数量
- special_requirements: 特殊要求
- contact_name: 联系人姓名
- contact_phone: 联系电话
- flight_number: 航班号（如有）
- ota_reference: OTA参考号
- price: 价格
- currency: 货币单位`,

                imageAnalysis: `请分析这张图片中的订单信息，提取所有可见的文字和数据：

请特别注意：
- 日期和时间信息
- 地址和位置信息
- 联系人信息
- 价格信息
- 任何参考号或订单号

请返回JSON格式的结构化数据。`
            };
        }

        /**
         * 生成提示模板
         * @param {string} templateName - 模板名称
         * @param {Object} variables - 变量替换
         * @returns {string} 生成的提示
         */
        generate(templateName, variables = {}) {
            try {
                let template = this.templates[templateName];
                if (!template) {
                    throw new Error(`模板不存在: ${templateName}`);
                }

                // 替换变量
                Object.entries(variables).forEach(([key, value]) => {
                    const placeholder = `{${key}}`;
                    template = template.replace(new RegExp(placeholder, 'g'), value);
                });

                return template;
            } catch (error) {
                this.logger.error('提示模板生成失败:', error);
                return '';
            }
        }
    }

    // 导出到全局命名空间
    window.OTA.ai.gemini.FlightNumberProcessor = FlightNumberProcessor;
    window.OTA.ai.gemini.DataNormalizer = DataNormalizer;
    window.OTA.ai.gemini.OTAReferenceEngine = OTAReferenceEngine;
    window.OTA.ai.gemini.AddressTranslator = AddressTranslator;
    window.OTA.ai.gemini.PromptTemplateEngine = PromptTemplateEngine;

    // 创建实例
    window.OTA.ai.gemini.flightProcessor = new FlightNumberProcessor();
    window.OTA.ai.gemini.dataNormalizer = new DataNormalizer();
    window.OTA.ai.gemini.referenceEngine = new OTAReferenceEngine();
    window.OTA.ai.gemini.addressTranslator = new AddressTranslator();
    window.OTA.ai.gemini.promptEngine = new PromptTemplateEngine();

    console.log('✅ Gemini核心功能模块已加载');

})();
