/**
 * 权限管理器
 * 统一管理浏览器权限请求，避免突然的权限弹窗
 * 遵循"用户主动"原则
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.core = window.OTA.core || {};

(function() {
    'use strict';

    /**
     * 权限管理器类
     */
    class PermissionManager {
        constructor() {
            this.logger = window.getLogger?.() || {
                log: console.log.bind(console),
                logError: console.error.bind(console)
            };

            this.permissionStates = new Map();
            this.userPreferences = this.loadUserPreferences();
        }

        /**
         * 检查权限状态（不请求权限）
         * @param {string} permission - 权限类型
         * @returns {string} 权限状态
         */
        checkPermissionStatus(permission) {
            switch (permission) {
                case 'notifications':
                    return 'Notification' in window ? Notification.permission : 'unsupported';
                case 'geolocation':
                    return 'geolocation' in navigator ? 'supported' : 'unsupported';
                default:
                    return 'unknown';
            }
        }

        /**
         * 用户主动请求权限
         * @param {string} permission - 权限类型
         * @param {Object} options - 选项
         * @returns {Promise<string>} 权限结果
         */
        async requestPermission(permission, options = {}) {
            const { reason = '', silent = false } = options;

            // 检查用户是否已拒绝
            if (this.userPreferences[permission] === 'denied') {
                this.logger.log(`用户已永久拒绝 ${permission} 权限`, 'info');
                return 'denied';
            }

            try {
                let result;

                switch (permission) {
                    case 'notifications':
                        if (!('Notification' in window)) {
                            return 'unsupported';
                        }

                        // 显示权限请求说明
                        if (!silent && reason) {
                            const confirmed = await this.showPermissionExplanation(
                                '通知权限',
                                reason,
                                '我们需要通知权限来及时提醒您重要的系统状态变化。'
                            );
                            
                            if (!confirmed) {
                                this.saveUserPreference(permission, 'denied');
                                return 'denied';
                            }
                        }

                        result = await Notification.requestPermission();
                        break;

                    case 'geolocation':
                        if (!('geolocation' in navigator)) {
                            return 'unsupported';
                        }

                        // 地理位置权限需要用户交互
                        result = await new Promise((resolve) => {
                            navigator.geolocation.getCurrentPosition(
                                () => resolve('granted'),
                                () => resolve('denied'),
                                { timeout: 5000 }
                            );
                        });
                        break;

                    default:
                        throw new Error(`不支持的权限类型: ${permission}`);
                }

                // 保存用户选择
                this.saveUserPreference(permission, result);
                this.permissionStates.set(permission, result);

                this.logger.log(`权限请求结果: ${permission} = ${result}`, 'info');
                return result;

            } catch (error) {
                this.logger.logError(`权限请求失败: ${permission}`, error);
                return 'error';
            }
        }

        /**
         * 显示权限说明对话框
         * @param {string} title - 标题
         * @param {string} reason - 请求原因
         * @param {string} description - 详细说明
         * @returns {Promise<boolean>} 用户是否同意
         */
        async showPermissionExplanation(title, reason, description) {
            return new Promise((resolve) => {
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'permission-modal';
                modal.innerHTML = `
                    <div class="permission-modal-content">
                        <div class="permission-modal-header">
                            <h3>🔒 ${title}</h3>
                        </div>
                        <div class="permission-modal-body">
                            <p class="permission-reason"><strong>请求原因：</strong>${reason}</p>
                            <p class="permission-description">${description}</p>
                            <p class="permission-note">
                                <small>💡 您可以随时在浏览器设置中修改权限选择</small>
                            </p>
                        </div>
                        <div class="permission-modal-footer">
                            <button type="button" class="btn btn-outline permission-deny">稍后再说</button>
                            <button type="button" class="btn btn-primary permission-allow">允许</button>
                        </div>
                    </div>
                `;

                // 添加样式
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                `;

                const content = modal.querySelector('.permission-modal-content');
                content.style.cssText = `
                    background: white;
                    border-radius: 8px;
                    padding: 24px;
                    max-width: 400px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                `;

                // 绑定事件
                modal.querySelector('.permission-allow').addEventListener('click', () => {
                    document.body.removeChild(modal);
                    resolve(true);
                });

                modal.querySelector('.permission-deny').addEventListener('click', () => {
                    document.body.removeChild(modal);
                    resolve(false);
                });

                // 点击外部关闭
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                        resolve(false);
                    }
                });

                document.body.appendChild(modal);
            });
        }

        /**
         * 加载用户权限偏好
         * @returns {Object} 用户偏好
         */
        loadUserPreferences() {
            try {
                const saved = localStorage.getItem('ota_permission_preferences');
                return saved ? JSON.parse(saved) : {};
            } catch (error) {
                this.logger.logError('加载权限偏好失败', error);
                return {};
            }
        }

        /**
         * 保存用户权限偏好
         * @param {string} permission - 权限类型
         * @param {string} choice - 用户选择
         */
        saveUserPreference(permission, choice) {
            try {
                this.userPreferences[permission] = choice;
                localStorage.setItem('ota_permission_preferences', JSON.stringify(this.userPreferences));
            } catch (error) {
                this.logger.logError('保存权限偏好失败', error);
            }
        }

        /**
         * 重置用户权限偏好
         * @param {string} permission - 权限类型（可选）
         */
        resetUserPreferences(permission = null) {
            if (permission) {
                delete this.userPreferences[permission];
            } else {
                this.userPreferences = {};
            }
            
            try {
                localStorage.setItem('ota_permission_preferences', JSON.stringify(this.userPreferences));
                this.logger.log(`权限偏好已重置: ${permission || '全部'}`, 'info');
            } catch (error) {
                this.logger.logError('重置权限偏好失败', error);
            }
        }

        /**
         * 获取权限管理器状态
         * @returns {Object} 状态信息
         */
        getStatus() {
            return {
                permissions: Object.fromEntries(this.permissionStates),
                userPreferences: { ...this.userPreferences },
                supportedPermissions: [
                    'Notification' in window ? 'notifications' : null,
                    'geolocation' in navigator ? 'geolocation' : null
                ].filter(Boolean)
            };
        }
    }

    // 创建全局实例
    const permissionManager = new PermissionManager();

    // 导出到OTA命名空间
    window.OTA.core.permissionManager = permissionManager;
    window.OTA.getPermissionManager = () => permissionManager;

    // 向后兼容
    window.getPermissionManager = () => permissionManager;

    console.log('🔒 权限管理器已加载');

})();
