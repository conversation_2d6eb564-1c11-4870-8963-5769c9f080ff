/**
 * Gemini AI 处理器模块
 * 合并原 js/ai/gemini/processors/ 目录下的所有处理器
 * <AUTHOR> System
 * @created 2025-01-29
 */

window.OTA = window.OTA || {};
window.OTA.ai = window.OTA.ai || {};
window.OTA.ai.gemini = window.OTA.ai.gemini || {};
window.OTA.ai.gemini.processors = window.OTA.ai.gemini.processors || {};

(function() {
    'use strict';

    // 获取依赖服务的函数
    function getLogger() {
        return window.OTA?.services?.logger || console;
    }

    /**
     * 基础处理器类
     * 原文件：js/ai/gemini/core/base-processor.js
     */
    class BaseProcessor {
        constructor(name) {
            this.name = name;
            this.logger = getLogger();
        }

        /**
         * 处理订单数据
         * @param {string} orderText - 订单文本
         * @returns {Object} 处理结果
         */
        async process(orderText) {
            throw new Error('子类必须实现 process 方法');
        }

        /**
         * 验证处理结果
         * @param {Object} result - 处理结果
         * @returns {boolean} 是否有效
         */
        validate(result) {
            return result && typeof result === 'object';
        }

        /**
         * 标准化字段名
         * @param {Object} data - 原始数据
         * @returns {Object} 标准化后的数据
         */
        normalizeFields(data) {
            const fieldMappings = {
                'pickup_date': 'date',
                'pickup_time': 'time',
                'pickup_location': 'pickup',
                'dropoff_location': 'destination',
                'luggage_count': 'luggage_number'
            };

            const normalized = { ...data };
            Object.entries(fieldMappings).forEach(([oldField, newField]) => {
                if (data[oldField] && !data[newField]) {
                    normalized[newField] = data[oldField];
                }
            });

            return normalized;
        }
    }

    /**
     * Chong Dealer 处理器
     * 原文件：js/ai/gemini/processors/chong-dealer-processor.js
     */
    class ChongDealerProcessor extends BaseProcessor {
        constructor() {
            super('ChongDealer');
            this.patterns = {
                date: /(\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/g,
                time: /(\d{1,2}[：:]\d{2})/g,
                phone: /(\+?6?01[0-9-]{8,10})/g,
                flight: /([A-Z]{2,3}\s*\d{3,4})/g,
                price: /(RM\s*\d+(?:\.\d{2})?)/g
            };
        }

        async process(orderText) {
            try {
                const result = {
                    ota_type: 'chong-dealer',
                    raw_text: orderText
                };

                // 提取日期
                const dateMatch = orderText.match(this.patterns.date);
                if (dateMatch) {
                    result.pickup_date = this.formatDate(dateMatch[0]);
                }

                // 提取时间
                const timeMatch = orderText.match(this.patterns.time);
                if (timeMatch) {
                    result.pickup_time = timeMatch[0].replace('：', ':');
                }

                // 提取电话号码
                const phoneMatch = orderText.match(this.patterns.phone);
                if (phoneMatch) {
                    result.contact_phone = phoneMatch[0];
                }

                // 提取航班号
                const flightMatch = orderText.match(this.patterns.flight);
                if (flightMatch) {
                    result.flight_number = flightMatch[0].replace(/\s+/g, '');
                }

                // 提取价格
                const priceMatch = orderText.match(this.patterns.price);
                if (priceMatch) {
                    result.price = priceMatch[0].replace('RM', '').trim();
                    result.currency = 'MYR';
                }

                // 提取地址信息
                this.extractAddresses(orderText, result);

                // 生成OTA参考号
                result.ota_reference_number = this.generateReference(result);

                return this.normalizeFields(result);
            } catch (error) {
                this.logger.error('Chong Dealer处理失败:', error);
                return { error: error.message, ota_type: 'chong-dealer' };
            }
        }

        extractAddresses(text, result) {
            // 简化的地址提取逻辑
            const lines = text.split('\n').filter(line => line.trim());
            
            // 查找包含地址关键词的行
            const addressKeywords = ['Airport', 'Hotel', 'Mall', 'Station', 'Terminal'];
            const addressLines = lines.filter(line => 
                addressKeywords.some(keyword => line.includes(keyword))
            );

            if (addressLines.length >= 2) {
                result.pickup_location = addressLines[0].trim();
                result.dropoff_location = addressLines[1].trim();
            } else if (addressLines.length === 1) {
                result.pickup_location = addressLines[0].trim();
            }
        }

        formatDate(dateStr) {
            // 转换为 DD-MM-YYYY 格式
            const parts = dateStr.split(/[-\/]/);
            if (parts.length === 3) {
                return `${parts[0].padStart(2, '0')}-${parts[1].padStart(2, '0')}-${parts[2]}`;
            }
            return dateStr;
        }

        generateReference(data) {
            const timestamp = Date.now().toString().slice(-6);
            return `CD${timestamp}`;
        }
    }

    /**
     * Agoda 处理器
     * 原文件：js/ai/gemini/processors/agoda-processor.js
     */
    class AgodaProcessor extends BaseProcessor {
        constructor() {
            super('Agoda');
        }

        async process(orderText) {
            try {
                const result = {
                    ota_type: 'agoda',
                    raw_text: orderText
                };

                // Agoda特定的处理逻辑
                if (orderText.toLowerCase().includes('agoda')) {
                    result.ota_reference_number = this.extractAgodaReference(orderText);
                }

                // 通用字段提取
                this.extractCommonFields(orderText, result);

                return this.normalizeFields(result);
            } catch (error) {
                this.logger.error('Agoda处理失败:', error);
                return { error: error.message, ota_type: 'agoda' };
            }
        }

        extractAgodaReference(text) {
            const refPattern = /(?:Booking|Reference|Ref)[:\s]*([A-Z0-9]{6,12})/i;
            const match = text.match(refPattern);
            return match ? match[1] : `AG${Date.now().toString().slice(-6)}`;
        }

        extractCommonFields(text, result) {
            // 通用字段提取逻辑
            const datePattern = /(\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/;
            const timePattern = /(\d{1,2}[：:]\d{2})/;
            
            const dateMatch = text.match(datePattern);
            if (dateMatch) {
                result.pickup_date = dateMatch[1];
            }

            const timeMatch = text.match(timePattern);
            if (timeMatch) {
                result.pickup_time = timeMatch[1].replace('：', ':');
            }
        }
    }

    /**
     * Booking.com 处理器
     * 原文件：js/ai/gemini/processors/booking-processor.js
     */
    class BookingProcessor extends BaseProcessor {
        constructor() {
            super('Booking');
        }

        async process(orderText) {
            try {
                const result = {
                    ota_type: 'booking',
                    raw_text: orderText
                };

                // Booking.com特定的处理逻辑
                if (orderText.toLowerCase().includes('booking')) {
                    result.ota_reference_number = this.extractBookingReference(orderText);
                }

                // 通用字段提取
                this.extractCommonFields(orderText, result);

                return this.normalizeFields(result);
            } catch (error) {
                this.logger.error('Booking处理失败:', error);
                return { error: error.message, ota_type: 'booking' };
            }
        }

        extractBookingReference(text) {
            const refPattern = /(?:Confirmation|Booking)[:\s]*([0-9]{8,12})/i;
            const match = text.match(refPattern);
            return match ? match[1] : `BK${Date.now().toString().slice(-6)}`;
        }

        extractCommonFields(text, result) {
            // 通用字段提取逻辑（与AgodaProcessor类似）
            const datePattern = /(\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/;
            const timePattern = /(\d{1,2}[：:]\d{2})/;
            
            const dateMatch = text.match(datePattern);
            if (dateMatch) {
                result.pickup_date = dateMatch[1];
            }

            const timeMatch = text.match(timePattern);
            if (timeMatch) {
                result.pickup_time = timeMatch[1].replace('：', ':');
            }
        }
    }

    /**
     * 通用处理器
     * 原文件：js/ai/gemini/processors/generic-processor.js
     */
    class GenericProcessor extends BaseProcessor {
        constructor() {
            super('Generic');
        }

        async process(orderText) {
            try {
                const result = {
                    ota_type: 'generic',
                    raw_text: orderText
                };

                // 通用字段提取
                this.extractAllFields(orderText, result);

                // 生成通用参考号
                result.ota_reference_number = `GEN${Date.now().toString().slice(-6)}`;

                return this.normalizeFields(result);
            } catch (error) {
                this.logger.error('通用处理失败:', error);
                return { error: error.message, ota_type: 'generic' };
            }
        }

        extractAllFields(text, result) {
            // 提取所有可能的字段
            const patterns = {
                date: /(\d{1,2}[-\/]\d{1,2}[-\/]\d{4})/g,
                time: /(\d{1,2}[：:]\d{2})/g,
                phone: /(\+?6?01[0-9-]{8,10})/g,
                email: /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g,
                price: /((?:RM|USD|SGD)\s*\d+(?:\.\d{2})?)/g
            };

            Object.entries(patterns).forEach(([field, pattern]) => {
                const matches = text.match(pattern);
                if (matches) {
                    switch (field) {
                        case 'date':
                            result.pickup_date = matches[0];
                            break;
                        case 'time':
                            result.pickup_time = matches[0].replace('：', ':');
                            break;
                        case 'phone':
                            result.contact_phone = matches[0];
                            break;
                        case 'email':
                            result.contact_email = matches[0];
                            break;
                        case 'price':
                            result.price = matches[0].replace(/[A-Z]+\s*/, '');
                            result.currency = matches[0].match(/[A-Z]+/)[0];
                            break;
                    }
                }
            });
        }
    }

    // 导出到全局命名空间
    window.OTA.ai.gemini.processors.BaseProcessor = BaseProcessor;
    window.OTA.ai.gemini.processors.ChongDealerProcessor = ChongDealerProcessor;
    window.OTA.ai.gemini.processors.AgodaProcessor = AgodaProcessor;
    window.OTA.ai.gemini.processors.BookingProcessor = BookingProcessor;
    window.OTA.ai.gemini.processors.GenericProcessor = GenericProcessor;

    // 创建处理器实例
    window.OTA.ai.gemini.processors.chongDealer = new ChongDealerProcessor();
    window.OTA.ai.gemini.processors.agoda = new AgodaProcessor();
    window.OTA.ai.gemini.processors.booking = new BookingProcessor();
    window.OTA.ai.gemini.processors.generic = new GenericProcessor();

    console.log('✅ Gemini处理器模块已加载');

})();
