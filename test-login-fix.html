<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录修复验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>登录修复验证测试</h1>
    <div id="results"></div>

    <!-- 只加载必要的核心依赖，模拟实际加载顺序 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/services/logger.js"></script>
    <script src="js/utils/utils.js"></script>
    <script src="js/bootstrap/app-state.js"></script>
    <script src="js/services/api-service.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function runLoginFixTests() {
            addResult('开始登录修复验证测试...', 'info');

            // 测试关键函数是否存在且不冲突
            addResult('=== 检查关键函数定义 ===', 'info');
            
            const functions = [
                { name: 'window.getAppState', getter: () => window.getAppState },
                { name: 'window.getLogger', getter: () => window.getLogger },
                { name: 'window.getAPIService', getter: () => window.getAPIService },
                { name: 'window.appState', getter: () => window.appState }
            ];

            let allFunctionsOk = true;

            functions.forEach(({ name, getter }) => {
                try {
                    const func = getter();
                    if (typeof func === 'function' || (name === 'window.appState' && typeof func === 'object')) {
                        addResult(`✅ ${name} 存在且类型正确`, 'success');
                    } else {
                        addResult(`❌ ${name} 类型错误: ${typeof func}`, 'error');
                        allFunctionsOk = false;
                    }
                } catch (error) {
                    addResult(`❌ ${name} 访问失败: ${error.message}`, 'error');
                    allFunctionsOk = false;
                }
            });

            // 测试函数调用链
            addResult('=== 测试函数调用链 ===', 'info');
            
            try {
                // 测试 getLogger 调用
                const logger = window.getLogger();
                if (logger && typeof logger.logUserAction === 'function') {
                    addResult('✅ getLogger() 返回有效的logger对象', 'success');
                } else {
                    addResult('❌ getLogger() 返回的对象缺少logUserAction方法', 'error');
                    allFunctionsOk = false;
                }
            } catch (error) {
                addResult(`❌ getLogger() 调用失败: ${error.message}`, 'error');
                allFunctionsOk = false;
            }

            try {
                // 测试 getAppState 调用
                const appState = window.getAppState();
                if (appState && typeof appState.get === 'function') {
                    addResult('✅ getAppState() 返回有效的appState对象', 'success');
                } else {
                    addResult('❌ getAppState() 返回的对象缺少get方法', 'error');
                    allFunctionsOk = false;
                }
            } catch (error) {
                addResult(`❌ getAppState() 调用失败: ${error.message}`, 'error');
                allFunctionsOk = false;
            }

            try {
                // 测试 getAPIService 调用
                const apiService = window.getAPIService();
                if (apiService && typeof apiService.login === 'function') {
                    addResult('✅ getAPIService() 返回有效的apiService对象', 'success');
                } else {
                    addResult('❌ getAPIService() 返回的对象缺少login方法', 'error');
                    allFunctionsOk = false;
                }
            } catch (error) {
                addResult(`❌ getAPIService() 调用失败: ${error.message}`, 'error');
                allFunctionsOk = false;
            }

            // 测试登录关键路径
            addResult('=== 测试登录关键路径 ===', 'info');
            
            try {
                // 模拟登录过程中的关键调用
                const logger = window.getLogger();
                
                // 测试 getCurrentUser (登录时会调用)
                if (typeof logger.getCurrentUser === 'function') {
                    const user = logger.getCurrentUser();
                    addResult('✅ logger.getCurrentUser() 调用成功', 'success');
                } else {
                    addResult('❌ logger对象缺少getCurrentUser方法', 'error');
                    allFunctionsOk = false;
                }

                // 测试 logUserAction (登录时会调用)
                if (typeof logger.logUserAction === 'function') {
                    // 不实际调用，只测试方法存在
                    addResult('✅ logger.logUserAction() 方法存在', 'success');
                } else {
                    addResult('❌ logger对象缺少logUserAction方法', 'error');
                    allFunctionsOk = false;
                }

            } catch (error) {
                addResult(`❌ 登录关键路径测试失败: ${error.message}`, 'error');
                allFunctionsOk = false;
            }

            // 检查是否有废弃警告
            addResult('=== 检查废弃警告 ===', 'info');
            
            // 重写console.warn来捕获警告
            const originalWarn = console.warn;
            let warningCount = 0;
            console.warn = function(...args) {
                if (args[0] && args[0].includes('DEPRECATED')) {
                    warningCount++;
                    addResult(`⚠️ 发现废弃警告: ${args[0]}`, 'warning');
                }
                originalWarn.apply(console, args);
            };

            // 触发一些调用来检查是否有警告
            try {
                window.appState?.get('test');
                window.getAppState();
                window.getLogger();
                window.getAPIService();
                
                setTimeout(() => {
                    console.warn = originalWarn; // 恢复原始console.warn
                    
                    if (warningCount === 0) {
                        addResult('✅ 未发现废弃警告', 'success');
                    } else {
                        addResult(`⚠️ 发现 ${warningCount} 个废弃警告`, 'warning');
                    }

                    // 总结
                    addResult('=== 测试总结 ===', 'info');
                    if (allFunctionsOk && warningCount === 0) {
                        addResult('🎉 所有测试通过！登录功能应该已修复', 'success');
                    } else if (allFunctionsOk) {
                        addResult('✅ 核心功能正常，但有废弃警告', 'warning');
                    } else {
                        addResult('❌ 存在功能问题，需要进一步修复', 'error');
                    }
                }, 100);
                
            } catch (error) {
                console.warn = originalWarn;
                addResult(`❌ 废弃警告检查失败: ${error.message}`, 'error');
            }
        }

        // 等待页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runLoginFixTests, 1000);
        });
    </script>
</body>
</html>