/**
 * 智能学习型格式预处理引擎 - 学习效果评估系统
 * 负责评估学习系统的效果，计算各种指标，分析趋势，生成报告
 *
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    // 获取依赖模块
    function getLearningConfig() {
        return window.OTA.learningConfig || window.learningConfig;
    }

    function getLearningStorageManager() {
        return window.OTA.learningStorageManager || window.learningStorageManager;
    }

    function getUserOperationLearner() {
        return window.OTA.userOperationLearner || window.userOperationLearner;
    }

    function getRuleGenerationEngine() {
        return window.OTA.ruleGenerationEngine || window.ruleGenerationEngine;
    }

    function getPredictiveCorrector() {
        return window.OTA.predictiveCorrector || window.predictiveCorrector;
    }

    function getAdaptivePromptOptimizer() {
        return window.OTA.adaptivePromptOptimizer || window.adaptivePromptOptimizer;
    }

    function getLogger() {
        return window.OTA.logger || window.logger;
    }

    /**
     * 学习效果评估器类
     * 评估和分析学习系统的整体效果
     */
    class LearningEffectivenessEvaluator {
        constructor() {
            this.config = getLearningConfig();
            this.storageManager = getLearningStorageManager();
            this.operationLearner = getUserOperationLearner();
            this.ruleEngine = getRuleGenerationEngine();
            this.predictiveCorrector = getPredictiveCorrector();
            this.promptOptimizer = getAdaptivePromptOptimizer();
            this.logger = getLogger();

            this.version = '1.0.0';

            // 评估配置
            this.evaluationConfig = {
                evaluationInterval: 24 * 60 * 60 * 1000, // 24小时
                trendAnalysisPeriod: 7 * 24 * 60 * 60 * 1000, // 7天
                baselineWindow: 30 * 24 * 60 * 60 * 1000, // 30天基线
                significanceThreshold: 0.05
            };

            // 指标定义
            this.metrics = this.initializeMetrics();

            // 评估历史
            this.evaluationHistory = [];

            // 当前评估结果
            this.currentEvaluation = null;

            this.initialize();
        }

        /**
         * 初始化学习效果评估器
         */
        initialize() {
            try {
                // 加载历史评估数据
                this.loadEvaluationHistory();

                // 设置定期评估
                this.setupPeriodicEvaluation();

                this.logger?.log('学习效果评估器初始化完成', 'info', {
                    version: this.version,
                    metricsCount: Object.keys(this.metrics).length,
                    evaluationInterval: this.evaluationConfig.evaluationInterval / (60 * 60 * 1000) + '小时'
                });

            } catch (error) {
                this.logger?.logError('学习效果评估器初始化失败', error);
            }
        }

        /**
         * 初始化指标定义
         */
        initializeMetrics() {
            return {
                // 准确性指标
                accuracy: {
                    name: '准确性',
                    description: '学习系统预测和校正的准确率',
                    calculator: this.calculateAccuracyMetrics.bind(this),
                    weight: 0.4,
                    target: 0.85
                },

                // 效率指标
                efficiency: {
                    name: '效率',
                    description: '处理速度和资源使用效率',
                    calculator: this.calculateEfficiencyMetrics.bind(this),
                    weight: 0.25,
                    target: 0.8
                },

                // 学习能力指标
                learningCapability: {
                    name: '学习能力',
                    description: '系统从用户操作中学习的能力',
                    calculator: this.calculateLearningCapabilityMetrics.bind(this),
                    weight: 0.2,
                    target: 0.75
                },

                // 用户满意度指标
                userSatisfaction: {
                    name: '用户满意度',
                    description: '用户对系统表现的满意程度',
                    calculator: this.calculateUserSatisfactionMetrics.bind(this),
                    weight: 0.15,
                    target: 0.8
                }
            };
        }

        /**
         * 执行完整评估
         * @param {Object} options - 评估选项
         * @returns {Object} 评估结果
         */
        performEvaluation(options = {}) {
            try {
                const evaluationId = this.generateEvaluationId();
                const startTime = Date.now();

                this.logger?.log('开始学习效果评估', 'info', { evaluationId });

                // 收集基础数据
                const baselineData = this.collectBaselineData(options);

                // 计算各项指标
                const metricResults = {};
                let totalScore = 0;
                let totalWeight = 0;

                for (const [metricKey, metric] of Object.entries(this.metrics)) {
                    try {
                        const result = metric.calculator(baselineData, options);
                        metricResults[metricKey] = {
                            ...result,
                            weight: metric.weight,
                            target: metric.target,
                            achievement: result.score / metric.target
                        };

                        totalScore += result.score * metric.weight;
                        totalWeight += metric.weight;

                    } catch (error) {
                        this.logger?.logError(`计算指标失败: ${metricKey}`, error);
                        metricResults[metricKey] = {
                            score: 0,
                            error: error.message,
                            weight: metric.weight,
                            target: metric.target
                        };
                    }
                }

                // 计算综合分数
                const overallScore = totalWeight > 0 ? totalScore / totalWeight : 0;

                // 趋势分析
                const trendAnalysis = this.performTrendAnalysis(metricResults);

                // 生成建议
                const recommendations = this.generateRecommendations(metricResults, trendAnalysis);

                // 创建评估结果
                const evaluation = {
                    id: evaluationId,
                    timestamp: new Date().toISOString(),
                    duration: Date.now() - startTime,
                    overallScore: overallScore,
                    grade: this.calculateGrade(overallScore),
                    metrics: metricResults,
                    trendAnalysis: trendAnalysis,
                    recommendations: recommendations,
                    baselineData: baselineData,
                    options: options
                };

                // 保存评估结果
                this.saveEvaluation(evaluation);
                this.currentEvaluation = evaluation;

                this.logger?.log('学习效果评估完成', 'success', {
                    evaluationId,
                    overallScore: overallScore.toFixed(3),
                    grade: evaluation.grade,
                    duration: evaluation.duration + 'ms'
                });

                return evaluation;

            } catch (error) {
                this.logger?.logError('执行学习效果评估失败', error);
                return this.createErrorEvaluation(error);
            }
        }

        /**
         * 收集基线数据
         * @param {Object} options - 选项
         * @returns {Object} 基线数据
         */
        collectBaselineData(options) {
            const endTime = new Date();
            const startTime = new Date(endTime.getTime() - this.evaluationConfig.baselineWindow);

            return {
                timeRange: {
                    start: startTime.toISOString(),
                    end: endTime.toISOString(),
                    duration: this.evaluationConfig.baselineWindow
                },

                // 用户操作数据
                userOperations: this.operationLearner.queryOperations({
                    dateFrom: startTime.toISOString(),
                    dateTo: endTime.toISOString(),
                    limit: 1000
                }),

                // 规则数据
                learningRules: this.ruleEngine.getAllRules(),

                // 预测数据
                predictionStats: this.predictiveCorrector.getPredictionStats(),

                // 提示词优化数据
                promptOptimizationStats: this.promptOptimizer.getOptimizationStats(),

                // 系统统计
                systemStats: this.storageManager.getStorageUsage()
            };
        }

        /**
         * 计算准确性指标
         * @param {Object} baselineData - 基线数据
         * @param {Object} options - 选项
         * @returns {Object} 准确性指标结果
         */
        calculateAccuracyMetrics(baselineData, options) {
            const operations = baselineData.userOperations;
            const predictionStats = baselineData.predictionStats;

            // 用户更正准确率
            const correctionOperations = operations.filter(op => op.type === 'correction');
            const validationOperations = operations.filter(op => op.type === 'validation');

            const userCorrectionRate = correctionOperations.length / Math.max(operations.length, 1);
            const validationSuccessRate = validationOperations.length > 0 ?
                validationOperations.filter(op => op.confidence > 0.8).length / validationOperations.length : 1;

            // 预测准确率
            const predictionAccuracy = predictionStats.totalPredictions > 0 ?
                predictionStats.successfulPredictions / predictionStats.totalPredictions : 0;

            // 规则应用成功率
            const rules = baselineData.learningRules;
            const activeRules = rules.filter(rule => rule.active);
            const ruleEffectiveness = activeRules.length > 0 ?
                activeRules.reduce((sum, rule) => sum + (rule.successRate || 0), 0) / activeRules.length : 0;

            // 综合准确性分数
            const accuracyScore = (
                (1 - userCorrectionRate) * 0.3 +  // 更正率越低越好
                validationSuccessRate * 0.3 +
                predictionAccuracy * 0.25 +
                ruleEffectiveness * 0.15
            );

            return {
                score: accuracyScore,
                details: {
                    userCorrectionRate: userCorrectionRate,
                    validationSuccessRate: validationSuccessRate,
                    predictionAccuracy: predictionAccuracy,
                    ruleEffectiveness: ruleEffectiveness
                },
                breakdown: {
                    '用户更正率': (1 - userCorrectionRate).toFixed(3),
                    '验证成功率': validationSuccessRate.toFixed(3),
                    '预测准确率': predictionAccuracy.toFixed(3),
                    '规则有效性': ruleEffectiveness.toFixed(3)
                }
            };
        }

        /**
         * 计算效率指标
         * @param {Object} baselineData - 基线数据
         * @param {Object} options - 选项
         * @returns {Object} 效率指标结果
         */
        calculateEfficiencyMetrics(baselineData, options) {
            const predictionStats = baselineData.predictionStats;
            const systemStats = baselineData.systemStats;
            const promptStats = baselineData.promptOptimizationStats;

            // 响应时间效率
            const averageConfidence = predictionStats.averageConfidence || 0;
            const responseTimeEfficiency = averageConfidence; // 简化计算

            // 存储效率
            const storageEfficiency = systemStats.percentage < 80 ? 1 : Math.max(0, (100 - systemStats.percentage) / 20);

            // 缓存命中率
            const cacheEfficiency = 0.8; // 模拟值

            // Token节省率（基于提示词优化）
            const tokenSavingRate = promptStats.completedTests > 0 ? 0.15 : 0; // 假设优化后节省15%

            // 综合效率分数
            const efficiencyScore = (
                responseTimeEfficiency * 0.3 +
                storageEfficiency * 0.25 +
                cacheEfficiency * 0.25 +
                tokenSavingRate * 0.2
            );

            return {
                score: efficiencyScore,
                details: {
                    responseTimeEfficiency: responseTimeEfficiency,
                    storageEfficiency: storageEfficiency,
                    cacheEfficiency: cacheEfficiency,
                    tokenSavingRate: tokenSavingRate
                },
                breakdown: {
                    '响应时间效率': responseTimeEfficiency.toFixed(3),
                    '存储效率': storageEfficiency.toFixed(3),
                    '缓存效率': cacheEfficiency.toFixed(3),
                    'Token节省率': tokenSavingRate.toFixed(3)
                }
            };
        }

        /**
         * 计算学习能力指标
         * @param {Object} baselineData - 基线数据
         * @param {Object} options - 选项
         * @returns {Object} 学习能力指标结果
         */
        calculateLearningCapabilityMetrics(baselineData, options) {
            const operations = baselineData.userOperations;
            const rules = baselineData.learningRules;
            const promptStats = baselineData.promptOptimizationStats;

            // 学习规则生成率
            const recentRules = rules.filter(rule => {
                const ruleAge = Date.now() - new Date(rule.created).getTime();
                return ruleAge < this.evaluationConfig.baselineWindow;
            });
            const ruleGenerationRate = recentRules.length / Math.max(operations.length, 1);

            // 模式识别能力
            const patternRecognitionScore = operations.length > 0 ?
                operations.filter(op => op.confidence > 0.7).length / operations.length : 0;

            // 适应性学习
            const adaptiveLearningScore = promptStats.activeTests > 0 ? 0.8 : 0.5;

            // 知识积累
            const knowledgeAccumulationScore = Math.min(1, rules.length / 100); // 假设100个规则为满分

            // 综合学习能力分数
            const learningCapabilityScore = (
                ruleGenerationRate * 0.3 +
                patternRecognitionScore * 0.3 +
                adaptiveLearningScore * 0.2 +
                knowledgeAccumulationScore * 0.2
            );

            return {
                score: learningCapabilityScore,
                details: {
                    ruleGenerationRate: ruleGenerationRate,
                    patternRecognitionScore: patternRecognitionScore,
                    adaptiveLearningScore: adaptiveLearningScore,
                    knowledgeAccumulationScore: knowledgeAccumulationScore
                },
                breakdown: {
                    '规则生成率': ruleGenerationRate.toFixed(3),
                    '模式识别能力': patternRecognitionScore.toFixed(3),
                    '适应性学习': adaptiveLearningScore.toFixed(3),
                    '知识积累': knowledgeAccumulationScore.toFixed(3)
                }
            };
        }

        /**
         * 计算用户满意度指标
         * @param {Object} baselineData - 基线数据
         * @param {Object} options - 选项
         * @returns {Object} 用户满意度指标结果
         */
        calculateUserSatisfactionMetrics(baselineData, options) {
            const operations = baselineData.userOperations;

            // 用户反馈分析
            const feedbackOperations = operations.filter(op => op.type === 'feedback');
            const positiveFeedbackRate = feedbackOperations.length > 0 ?
                feedbackOperations.filter(op => op.confidence > 0.7).length / feedbackOperations.length : 0.5;

            // 使用频率
            const usageFrequency = Math.min(1, operations.length / 100); // 假设100次操作为高频使用

            // 错误率（用户更正频率）
            const errorRate = operations.filter(op => op.type === 'correction').length / Math.max(operations.length, 1);
            const errorSatisfaction = Math.max(0, 1 - errorRate * 2); // 错误率越低满意度越高

            // 功能采用率
            const featureAdoptionRate = 0.7; // 模拟值

            // 综合用户满意度分数
            const userSatisfactionScore = (
                positiveFeedbackRate * 0.4 +
                usageFrequency * 0.2 +
                errorSatisfaction * 0.25 +
                featureAdoptionRate * 0.15
            );

            return {
                score: userSatisfactionScore,
                details: {
                    positiveFeedbackRate: positiveFeedbackRate,
                    usageFrequency: usageFrequency,
                    errorSatisfaction: errorSatisfaction,
                    featureAdoptionRate: featureAdoptionRate
                },
                breakdown: {
                    '正面反馈率': positiveFeedbackRate.toFixed(3),
                    '使用频率': usageFrequency.toFixed(3),
                    '错误满意度': errorSatisfaction.toFixed(3),
                    '功能采用率': featureAdoptionRate.toFixed(3)
                }
            };
        }

        /**
         * 执行趋势分析
         * @param {Object} currentMetrics - 当前指标
         * @returns {Object} 趋势分析结果
         */
        performTrendAnalysis(currentMetrics) {
            try {
                const recentEvaluations = this.getRecentEvaluations(5); // 获取最近5次评估

                if (recentEvaluations.length < 2) {
                    return {
                        available: false,
                        message: '数据不足，无法进行趋势分析'
                    };
                }

                const trends = {};

                // 分析每个指标的趋势
                Object.keys(currentMetrics).forEach(metricKey => {
                    const historicalScores = recentEvaluations.map(eval =>
                        eval.metrics[metricKey]?.score || 0
                    );

                    trends[metricKey] = this.calculateTrend(historicalScores);
                });

                // 分析整体趋势
                const overallScores = recentEvaluations.map(eval => eval.overallScore);
                trends.overall = this.calculateTrend(overallScores);

                return {
                    available: true,
                    trends: trends,
                    period: `最近${recentEvaluations.length}次评估`,
                    analysis: this.generateTrendAnalysis(trends)
                };

            } catch (error) {
                this.logger?.logError('趋势分析失败', error);
                return {
                    available: false,
                    error: error.message
                };
            }
        }

        /**
         * 计算单个指标趋势
         * @param {Array} scores - 分数数组
         * @returns {Object} 趋势信息
         */
        calculateTrend(scores) {
            if (scores.length < 2) {
                return { direction: 'stable', change: 0, confidence: 0 };
            }

            // 简单线性回归计算趋势
            const n = scores.length;
            const x = Array.from({length: n}, (_, i) => i);
            const y = scores;

            const sumX = x.reduce((a, b) => a + b, 0);
            const sumY = y.reduce((a, b) => a + b, 0);
            const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
            const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

            const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
            const intercept = (sumY - slope * sumX) / n;

            // 计算R²
            const yMean = sumY / n;
            const ssTotal = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
            const ssRes = y.reduce((sum, yi, i) => {
                const predicted = slope * x[i] + intercept;
                return sum + Math.pow(yi - predicted, 2);
            }, 0);
            const rSquared = 1 - (ssRes / ssTotal);

            // 确定趋势方向
            let direction = 'stable';
            if (Math.abs(slope) > 0.01) {
                direction = slope > 0 ? 'improving' : 'declining';
            }

            return {
                direction: direction,
                change: slope,
                confidence: rSquared,
                latest: scores[scores.length - 1],
                previous: scores[scores.length - 2],
                improvement: scores[scores.length - 1] - scores[scores.length - 2]
            };
        }

        /**
         * 生成趋势分析
         * @param {Object} trends - 趋势数据
         * @returns {Array} 分析结果
         */
        generateTrendAnalysis(trends) {
            const analysis = [];

            // 整体趋势分析
            const overallTrend = trends.overall;
            if (overallTrend.direction === 'improving') {
                analysis.push({
                    type: 'positive',
                    message: `整体表现呈上升趋势，改善幅度: ${(overallTrend.improvement * 100).toFixed(1)}%`
                });
            } else if (overallTrend.direction === 'declining') {
                analysis.push({
                    type: 'negative',
                    message: `整体表现呈下降趋势，下降幅度: ${(Math.abs(overallTrend.improvement) * 100).toFixed(1)}%`
                });
            } else {
                analysis.push({
                    type: 'neutral',
                    message: '整体表现保持稳定'
                });
            }

            // 各指标趋势分析
            Object.entries(trends).forEach(([metricKey, trend]) => {
                if (metricKey === 'overall') return;

                const metricName = this.metrics[metricKey]?.name || metricKey;

                if (trend.direction === 'improving' && trend.confidence > 0.5) {
                    analysis.push({
                        type: 'positive',
                        message: `${metricName}显著改善，置信度: ${(trend.confidence * 100).toFixed(1)}%`
                    });
                } else if (trend.direction === 'declining' && trend.confidence > 0.5) {
                    analysis.push({
                        type: 'negative',
                        message: `${metricName}出现下降，需要关注`
                    });
                }
            });

            return analysis;
        }

        /**
         * 生成改进建议
         * @param {Object} metrics - 指标结果
         * @param {Object} trendAnalysis - 趋势分析
         * @returns {Array} 建议列表
         */
        generateRecommendations(metrics, trendAnalysis) {
            const recommendations = [];

            // 基于指标表现生成建议
            Object.entries(metrics).forEach(([metricKey, result]) => {
                const metric = this.metrics[metricKey];
                const achievement = result.achievement || 0;

                if (achievement < 0.7) {
                    recommendations.push({
                        priority: 'high',
                        category: metricKey,
                        title: `改善${metric.name}`,
                        description: this.getMetricRecommendation(metricKey, result),
                        impact: 'high'
                    });
                } else if (achievement < 0.9) {
                    recommendations.push({
                        priority: 'medium',
                        category: metricKey,
                        title: `优化${metric.name}`,
                        description: this.getMetricRecommendation(metricKey, result),
                        impact: 'medium'
                    });
                }
            });

            // 基于趋势分析生成建议
            if (trendAnalysis.available) {
                trendAnalysis.analysis.forEach(analysis => {
                    if (analysis.type === 'negative') {
                        recommendations.push({
                            priority: 'high',
                            category: 'trend',
                            title: '趋势预警',
                            description: analysis.message + '，建议立即采取改进措施',
                            impact: 'high'
                        });
                    }
                });
            }

            // 按优先级排序
            recommendations.sort((a, b) => {
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            });

            return recommendations.slice(0, 10); // 限制建议数量
        }

        /**
         * 获取指标改进建议
         * @param {string} metricKey - 指标键
         * @param {Object} result - 指标结果
         * @returns {string} 建议描述
         */
        getMetricRecommendation(metricKey, result) {
            const recommendations = {
                accuracy: '考虑增加训练数据，优化预测算法，或调整置信度阈值',
                efficiency: '优化缓存策略，减少不必要的计算，或升级硬件资源',
                learningCapability: '增加更多样化的训练案例，改进模式识别算法',
                userSatisfaction: '收集更多用户反馈，改善用户界面，或提供更好的帮助文档'
            };

            return recommendations[metricKey] || '请根据具体情况制定改进计划';
        }

        /**
         * 计算等级
         * @param {number} score - 分数
         * @returns {string} 等级
         */
        calculateGrade(score) {
            if (score >= 0.9) return 'A';
            if (score >= 0.8) return 'B';
            if (score >= 0.7) return 'C';
            if (score >= 0.6) return 'D';
            return 'F';
        }

        /**
         * 生成评估ID
         */
        generateEvaluationId() {
            return `eval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        /**
         * 保存评估结果
         * @param {Object} evaluation - 评估结果
         */
        saveEvaluation(evaluation) {
            try {
                this.evaluationHistory.push(evaluation);

                // 限制历史记录数量
                if (this.evaluationHistory.length > 50) {
                    this.evaluationHistory = this.evaluationHistory.slice(-50);
                }

                // 保存到存储
                const storageKey = `${this.config.get('storage.keyPrefix')}evaluations`;
                localStorage.setItem(storageKey, JSON.stringify(this.evaluationHistory));

            } catch (error) {
                this.logger?.logError('保存评估结果失败', error);
            }
        }

        /**
         * 加载评估历史
         */
        loadEvaluationHistory() {
            try {
                const storageKey = `${this.config.get('storage.keyPrefix')}evaluations`;
                const data = localStorage.getItem(storageKey);

                if (data) {
                    this.evaluationHistory = JSON.parse(data);
                }

            } catch (error) {
                this.logger?.logError('加载评估历史失败', error);
                this.evaluationHistory = [];
            }
        }

        /**
         * 获取最近的评估
         * @param {number} count - 数量
         * @returns {Array} 评估列表
         */
        getRecentEvaluations(count = 10) {
            return this.evaluationHistory
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                .slice(0, count);
        }

        /**
         * 设置定期评估
         */
        setupPeriodicEvaluation() {
            setInterval(() => {
                this.performEvaluation({ automatic: true });
            }, this.evaluationConfig.evaluationInterval);

            this.logger?.log('定期评估已设置', 'info', {
                interval: this.evaluationConfig.evaluationInterval / (60 * 60 * 1000) + '小时'
            });
        }

        /**
         * 创建错误评估结果
         * @param {Error} error - 错误对象
         * @returns {Object} 错误评估结果
         */
        createErrorEvaluation(error) {
            return {
                id: this.generateEvaluationId(),
                timestamp: new Date().toISOString(),
                error: true,
                message: error.message,
                overallScore: 0,
                grade: 'F'
            };
        }

        /**
         * 生成评估报告
         * @param {string} evaluationId - 评估ID
         * @returns {Object} 报告对象
         */
        generateReport(evaluationId = null) {
            const evaluation = evaluationId ?
                this.evaluationHistory.find(e => e.id === evaluationId) :
                this.currentEvaluation;

            if (!evaluation) {
                return null;
            }

            return {
                title: '学习效果评估报告',
                evaluationId: evaluation.id,
                timestamp: evaluation.timestamp,
                summary: {
                    overallScore: evaluation.overallScore,
                    grade: evaluation.grade,
                    status: this.getStatusDescription(evaluation.overallScore)
                },
                metrics: evaluation.metrics,
                trends: evaluation.trendAnalysis,
                recommendations: evaluation.recommendations,
                conclusion: this.generateConclusion(evaluation)
            };
        }

        /**
         * 获取状态描述
         * @param {number} score - 分数
         * @returns {string} 状态描述
         */
        getStatusDescription(score) {
            if (score >= 0.9) return '优秀 - 学习系统表现卓越';
            if (score >= 0.8) return '良好 - 学习系统表现良好';
            if (score >= 0.7) return '一般 - 学习系统表现一般，有改进空间';
            if (score >= 0.6) return '较差 - 学习系统需要改进';
            return '差 - 学习系统需要重大改进';
        }

        /**
         * 生成结论
         * @param {Object} evaluation - 评估结果
         * @returns {string} 结论
         */
        generateConclusion(evaluation) {
            const score = evaluation.overallScore;
            const grade = evaluation.grade;
            const highPriorityRecommendations = evaluation.recommendations.filter(r => r.priority === 'high').length;

            let conclusion = `本次评估显示学习系统整体得分为 ${(score * 100).toFixed(1)}%，等级为 ${grade}。`;

            if (highPriorityRecommendations > 0) {
                conclusion += ` 发现 ${highPriorityRecommendations} 个高优先级改进点，建议优先处理。`;
            }

            if (evaluation.trendAnalysis.available) {
                const overallTrend = evaluation.trendAnalysis.trends.overall;
                if (overallTrend.direction === 'improving') {
                    conclusion += ' 整体趋势向好，系统正在持续改进。';
                } else if (overallTrend.direction === 'declining') {
                    conclusion += ' 整体趋势下降，需要立即关注。';
                }
            }

            return conclusion;
        }

        /**
         * 获取当前评估结果
         * @returns {Object} 当前评估结果
         */
        getCurrentEvaluation() {
            return this.currentEvaluation;
        }

        /**
         * 获取评估历史
         * @returns {Array} 评估历史
         */
        getEvaluationHistory() {
            return [...this.evaluationHistory];
        }

        /**
         * 获取评估统计
         * @returns {Object} 统计信息
         */
        getEvaluationStats() {
            return {
                totalEvaluations: this.evaluationHistory.length,
                latestEvaluation: this.currentEvaluation?.timestamp,
                averageScore: this.evaluationHistory.length > 0 ?
                    this.evaluationHistory.reduce((sum, e) => sum + e.overallScore, 0) / this.evaluationHistory.length : 0,
                gradeDistribution: this.calculateGradeDistribution()
            };
        }

        /**
         * 计算等级分布
         * @returns {Object} 等级分布
         */
        calculateGradeDistribution() {
            const distribution = { A: 0, B: 0, C: 0, D: 0, F: 0 };

            this.evaluationHistory.forEach(evaluation => {
                if (evaluation.grade && distribution.hasOwnProperty(evaluation.grade)) {
                    distribution[evaluation.grade]++;
                }
            });

            return distribution;
        }
    }

    // 创建全局实例
    const learningEffectivenessEvaluator = new LearningEffectivenessEvaluator();

    // 导出到全局命名空间
    window.OTA.learningEffectivenessEvaluator = learningEffectivenessEvaluator;
    window.learningEffectivenessEvaluator = learningEffectivenessEvaluator; // 向后兼容

    // 工厂函数
    window.getLearningEffectivenessEvaluator = function() {
        return window.OTA.learningEffectivenessEvaluator || window.learningEffectivenessEvaluator;
    };

    console.log('学习效果评估器加载完成', {
        version: learningEffectivenessEvaluator.version,
        metricsCount: Object.keys(learningEffectivenessEvaluator.metrics).length,
        evaluationInterval: learningEffectivenessEvaluator.evaluationConfig.evaluationInterval / (60 * 60 * 1000) + '小时'
    });

})();