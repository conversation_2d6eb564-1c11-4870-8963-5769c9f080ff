/**
 * @OTA_SERVICE 日志管理器模块  
 * 🏷️ 标签: @OTA_LOGGER_SERVICE
 * 📝 说明: 负责记录系统操作、API调用、错误信息等，简化版本
 * ⚠️ 警告: 已注册，请勿重复开发
 * 重构为传统script标签加载方式
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

class Logger {
    constructor() {
        this.logs = [];
        this.maxLogs = 1000; // 最大日志条数
        this.debugMode = false;
        this.listeners = [];
        this.originalConsole = {}; // 存储原始console方法
        
        // 日志级别
        this.levels = {
            debug: 0,
            info: 1,
            success: 2,
            warning: 3,
            error: 4
        };
        
        // 基础监控配置
        this.monitoring = {
            enabled: true,
            realTimeConsole: true // 实时控制台输出
        };
        
        this.loadFromStorage();
        this.interceptConsole();
        this.setupBasicMonitoring();
    }
    
    /**
     * 从本地存储加载日志
     */
    loadFromStorage() {
        try {
            const saved = localStorage.getItem('ota-system-logs');
            if (saved) {
                this.logs = JSON.parse(saved);
                // 限制日志数量
                if (this.logs.length > this.maxLogs) {
                    this.logs = this.logs.slice(-this.maxLogs);
                }
            }
        } catch (error) {
            console.warn('加载日志失败:', error);
            this.logs = [];
        }
    }
    
    /**
     * 保存日志到本地存储
     */
    saveToStorage() {
        try {
            // 只保存最近的日志
            const logsToSave = this.logs.slice(-this.maxLogs);
            localStorage.setItem('ota-system-logs', JSON.stringify(logsToSave));
        } catch (error) {
            console.warn('保存日志失败:', error);
        }
    }
    
    /**
     * 安全地转换参数数组为字符串
     * @param {Array} args - 参数数组
     * @returns {string} 转换后的字符串
     */
    safeArgsToString(args) {
        try {
            return args.map(arg => {
                if (arg === null) return '[null]';
                if (arg === undefined) return '[undefined]';
                if (typeof arg === 'object') {
                    try {
                        return JSON.stringify(arg);
                    } catch (e) {
                        return '[Object]';
                    }
                }
                return String(arg);
            }).join(' ');
        } catch (error) {
            return '[转换参数失败]';
        }
    }

    /**
     * 拦截控制台输出
     */
    interceptConsole() {
        // 保存原始console方法
        this.originalConsole = {
            log: console.log.bind(console),
            info: console.info.bind(console),
            warn: console.warn.bind(console),
            error: console.error.bind(console),
            group: console.group.bind(console),
            groupCollapsed: console.groupCollapsed.bind(console),
            groupEnd: console.groupEnd.bind(console),
            time: console.time.bind(console),
            timeEnd: console.timeEnd.bind(console),
            table: this.createSafeTableMethod()
        };
        
        console.log = (...args) => {
            const message = this.safeArgsToString(args);
            this.log(message, 'info', null, false);
            this.originalConsole.log.apply(console, args);
        };
        
        console.info = (...args) => {
            const message = this.safeArgsToString(args);
            this.log(message, 'info', null, false);
            this.originalConsole.info.apply(console, args);
        };
        
        console.warn = (...args) => {
            const message = this.safeArgsToString(args);
            this.log(message, 'warning', null, false);
            this.originalConsole.warn.apply(console, args);
        };
        
        console.error = (...args) => {
            const message = this.safeArgsToString(args);
            this.log(message, 'error', null, false);
            this.originalConsole.error.apply(console, args);
        };
    }
    
    /**
     * 创建安全的table方法
     * 提供更强的兼容性和错误处理
     * @returns {function} 安全的table方法
     */
    createSafeTableMethod() {
        // 首先尝试使用原生console.table
        if (typeof console.table === 'function') {
            return (...args) => {
                try {
                    console.table.apply(console, args);
                } catch (error) {
                    // 如果原生方法失败，使用fallback
                    this.fallbackTable.apply(this, args);
                }
            };
        }
        
        // 如果原生方法不存在，直接使用fallback
        return this.fallbackTable.bind(this);
    }

    /**
     * 兼容性table方法实现
     * 在不支持console.table的浏览器中提供替代方案
     * @param {Object|Array} data - 要显示的数据
     * @param {Array} columns - 要显示的列（可选）
     */
    fallbackTable(data, columns = null) {
        try {
            if (!data) {
                this.originalConsole.log('(没有数据)');
                return;
            }
            
            // 防止循环引用和其他问题
            let safeData;
            try {
                safeData = JSON.parse(JSON.stringify(data));
            } catch (e) {
                safeData = String(data);
            }
            
            // 如果是对象，转换为数组格式
            let tableData = Array.isArray(safeData) ? safeData : Object.entries(safeData);
            
            // 如果是简单的键值对对象
            if (!Array.isArray(safeData) && typeof safeData === 'object') {
                this.originalConsole.log('📋 数据表格:');
                Object.entries(safeData).forEach(([key, value]) => {
                    this.originalConsole.log(`  ${key}: ${value}`);
                });
                return;
            }
            
            // 如果是数组，显示为列表
            if (Array.isArray(tableData) && tableData.length > 0) {
                this.originalConsole.log('📋 数据表格:');
                tableData.forEach((item, index) => {
                    if (typeof item === 'object' && item !== null) {
                        this.originalConsole.log(`  [${index}]:`);
                        Object.entries(item).forEach(([key, value]) => {
                            this.originalConsole.log(`    ${key}: ${value}`);
                        });
                    } else {
                        this.originalConsole.log(`  [${index}]: ${item}`);
                    }
                });
            } else {
                this.originalConsole.log('(空数据)');
            }
        } catch (error) {
            this.originalConsole.log('⚠️ 表格数据显示失败:', error.message);
            this.originalConsole.log('原始数据:', data);
        }
    }

    /**
     * 设置基础监控系统
     */
    setupBasicMonitoring() {
        if (!this.monitoring.enabled) return;
        
        this.log('🔍 初始化基础监控系统...', 'info', {
            type: 'monitoring_init'
        });
        
        // 监控未捕获的错误
        this.setupGlobalErrorHandling();
        
        this.log('✅ 基础监控系统初始化完成', 'success', {
            type: 'monitoring_ready'
        });
    }
    
    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        // 捕获JavaScript错误
        window.addEventListener('error', (event) => {
            this.logGlobalError('JavaScript错误', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });
        
        // 捕获Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.logGlobalError('未处理的Promise拒绝', {
                reason: event.reason,
                promise: event.promise
            });
        });
        
        this.log('全局错误处理已设置', 'info', { type: 'global_error_handler_init' });
    }
    
    /**
     * 记录日志
     * @param {string} message - 日志消息
     * @param {string} level - 日志级别
     * @param {any} data - 附加数据
     * @param {boolean} save - 是否保存到存储
     */
    log(message, level = 'info', data = null, save = true) {
        const levelStr = typeof level === 'string' ? level : String(level);
        const logEntry = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            level: levelStr.toLowerCase(),
            message: String(message),
            data: data ? this.sanitizeData(data) : null,
            source: this.getCallerInfo()
        };
        
        this.logs.push(logEntry);
        
        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(-this.maxLogs);
        }
        
        if (save) {
            this.saveToStorage();
        }
        
        // 通知监听器
        this.notifyListeners(logEntry);
        
        // 调试模式下输出到控制台
        if (this.debugMode) {
            this.outputToConsole(logEntry);
        }
    }
    
    /**
     * 获取调用者信息
     * @returns {string} 调用者信息
     */
    getCallerInfo() {
        try {
            const stack = new Error().stack;
            const lines = stack.split('\n');
            
            // 找到第一个不是logger的调用
            for (let i = 3; i < lines.length; i++) {
                const line = lines[i];
                if (line && !line.includes('logger.js') && !line.includes('console.')) {
                    const match = line.match(/at\s+(.+?)\s+\(/);
                    if (match) {
                        return match[1];
                    }
                }
            }
        } catch (error) {
            // 忽略错误
        }
        return 'unknown';
    }
    
    /**
     * 清理数据，避免循环引用
     * @param {any} data - 原始数据
     * @returns {any} 清理后的数据
     */
    sanitizeData(data) {
        try {
            // 使用JSON序列化来避免循环引用
            return JSON.parse(JSON.stringify(data, (key, value) => {
                // 过滤敏感信息
                if (typeof key === 'string' && 
                    (key.toLowerCase().includes('password') || 
                     key.toLowerCase().includes('token') ||
                     key.toLowerCase().includes('key'))) {
                    return '[FILTERED]';
                }
                return value;
            }));
        } catch (error) {
            return { error: '无法序列化数据', type: typeof data };
        }
    }
    
    /**
     * 输出到控制台
     * @param {object} logEntry - 日志条目
     */
    outputToConsole(logEntry) {
        if (!this.monitoring.realTimeConsole) return;
        
        const timestamp = new Date(logEntry.timestamp).toLocaleTimeString();
        const prefix = `[${timestamp}] [${logEntry.level.toUpperCase()}]`;
        
        // 根据日志级别使用不同的控制台方法
        switch (logEntry.level) {
            case 'error':
                this.originalConsole.error(`${prefix} ${logEntry.message}`, logEntry.data || '');
                break;
            case 'warning':
                this.originalConsole.warn(`${prefix} ${logEntry.message}`, logEntry.data || '');
                break;
            case 'success':
                this.originalConsole.info(`${prefix} ${logEntry.message}`, logEntry.data || '');
                break;
            default:
                this.originalConsole.log(`${prefix} ${logEntry.message}`, logEntry.data || '');
        }
    }
    
    /**
     * 记录全局错误
     * @param {string} errorType - 错误类型
     * @param {object} errorDetails - 错误详情
     */
    logGlobalError(errorType, errorDetails) {
        const message = `❌ 全局错误: ${errorType}`;
        const data = {
            type: 'global_error',
            errorType,
            details: errorDetails
        };
        
        this.log(message, 'error', data);
    }
    
    /**
     * 记录API调用
     * @param {string} url - API URL
     * @param {string} method - HTTP方法
     * @param {object} requestData - 请求数据
     * @param {object} responseData - 响应数据
     * @param {number} duration - 请求耗时
     */
    logApiCall(url, method, requestData = null, responseData = null, duration = null) {
        const message = `API调用: ${method.toUpperCase()} ${url}`;
        const data = {
            type: 'api_call',
            url,
            method: method.toUpperCase(),
            request: requestData,
            response: responseData,
            duration: duration ? `${duration}ms` : null
        };
        
        this.log(message, responseData?.status === false ? 'error' : 'info', data);
    }
    
    /**
     * 记录用户操作
     * @param {string} action - 操作类型
     * @param {object} details - 操作详情
     */
    logUserAction(action, details = null) {
        const message = `用户操作: ${action}`;
        const data = {
            type: 'user_action',
            action,
            details,
            user: this.getCurrentUser()
        };
        
        this.log(message, 'info', data);
    }
    
    /**
     * 记录Gemini AI交互
     * @param {string} input - 输入内容
     * @param {object} output - 输出结果
     * @param {number} confidence - 置信度
     */
    logGeminiInteraction(input, output, confidence = null) {
        const message = `Gemini AI解析: ${input.substring(0, 50)}...`;
        const data = {
            type: 'gemini_interaction',
            input: input.substring(0, 200), // 限制长度
            output,
            confidence,
            inputLength: input.length
        };
        
        this.log(message, output.success ? 'success' : 'warning', data);
    }
    
    /**
     * 记录数据变更
     * @param {string} field - 字段名
     * @param {any} oldValue - 旧值
     * @param {any} newValue - 新值
     * @param {string} source - 变更来源
     */
    logDataChange(field, oldValue, newValue, source = 'manual') {
        const message = `数据变更: ${field}`;
        const data = {
            type: 'data_change',
            field,
            oldValue,
            newValue,
            source
        };
        
        this.log(message, 'info', data);
    }
    
    /**
     * 记录错误
     * @param {Error|string} error - 错误对象或消息
     * @param {object} context - 错误上下文
     */
    logError(error, context = null) {
        const message = error instanceof Error ? error.message : String(error);
        const data = {
            type: 'error',
            error: error instanceof Error ? {
                message: error.message,
                stack: error.stack,
                name: error.name
            } : error,
            context
        };
        
        this.log(message, 'error', data);
    }
    
    /**
     * 获取当前用户信息
     * @returns {object|null} 用户信息
     */
    getCurrentUser() {
        try {
            return window.appState?.get('auth.user') || null;
        } catch (error) {
            return null;
        }
    }
    
    /**
     * 设置调试模式
     * @param {boolean} enabled - 是否启用
     */
    setDebugMode(enabled) {
        this.debugMode = Boolean(enabled);
        this.monitoring.realTimeConsole = enabled;
        this.log(`调试模式${enabled ? '启用' : '禁用'}`, 'info');
    }
    
    /**
     * 启用/禁用基础监控
     * @param {boolean} enabled - 是否启用
     */
    setMonitoringEnabled(enabled) {
        this.monitoring.enabled = Boolean(enabled);
        this.log(`基础监控${enabled ? '启用' : '禁用'}`, 'info', {
            type: 'monitoring_control'
        });
    }
    
    /**
     * 获取日志列表
     * @param {object} filters - 过滤条件
     * @returns {Array} 日志列表
     */
    getLogs(filters = {}) {
        let filteredLogs = [...this.logs];
        
        // 按级别过滤
        if (filters.level) {
            filteredLogs = filteredLogs.filter(log => log.level === filters.level);
        }
        
        // 按时间范围过滤
        if (filters.startTime) {
            const startTime = new Date(filters.startTime);
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startTime);
        }
        
        if (filters.endTime) {
            const endTime = new Date(filters.endTime);
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endTime);
        }
        
        // 按消息内容过滤
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filteredLogs = filteredLogs.filter(log => 
                log.message.toLowerCase().includes(searchTerm)
            );
        }
        
        // 按数量限制
        if (filters.limit) {
            filteredLogs = filteredLogs.slice(-filters.limit);
        }
        
        return filteredLogs.reverse(); // 最新的在前面
    }
    
    /**
     * 获取统计信息
     * @returns {object} 统计信息
     */
    getStats() {
        const stats = {
            total: this.logs.length,
            byLevel: {},
            byType: {},
            today: 0,
            lastHour: 0
        };
        
        const now = new Date();
        const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        
        this.logs.forEach(log => {
            // 按级别统计
            stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1;
            
            // 按类型统计
            if (log.data?.type) {
                stats.byType[log.data.type] = (stats.byType[log.data.type] || 0) + 1;
            }
            
            // 时间统计
            const logTime = new Date(log.timestamp);
            if (logTime >= todayStart) {
                stats.today++;
            }
            if (logTime >= hourAgo) {
                stats.lastHour++;
            }
        });
        
        return stats;
    }
    
    /**
     * 清空日志
     */
    clear() {
        this.logs = [];
        this.saveToStorage();
        this.log('日志已清空', 'info');
        this.notifyListeners({ type: 'clear' });
    }
    
    /**
     * 导出日志
     * @param {object} options - 导出选项
     * @returns {object} 导出结果
     */
    export(options = {}) {
        const filters = options.filters || {};
        const format = options.format || 'json';
        const logs = this.getLogs(filters);
        
        const exportData = {
            metadata: {
                exportTime: new Date().toISOString(),
                totalLogs: logs.length,
                filters,
                stats: this.getStats()
            },
            logs
        };
        
        switch (format) {
            case 'csv':
                return this.exportToCSV(logs);
            case 'txt':
                return this.exportToText(logs);
            default:
                return JSON.stringify(exportData, null, 2);
        }
    }
    
    /**
     * 导出为CSV格式
     * @param {Array} logs - 日志列表
     * @returns {string} CSV字符串
     */
    exportToCSV(logs) {
        const headers = ['时间', '级别', '消息', '来源', '数据'];
        const rows = [headers.join(',')];
        
        logs.forEach(log => {
            const row = [
                `"${log.timestamp}"`,
                `"${log.level}"`,
                `"${log.message.replace(/"/g, '""')}"`,
                `"${log.source}"`,
                `"${log.data ? JSON.stringify(log.data).replace(/"/g, '""') : ''}"`
            ];
            rows.push(row.join(','));
        });
        
        return rows.join('\n');
    }
    
    /**
     * 导出为文本格式
     * @param {Array} logs - 日志列表
     * @returns {string} 文本字符串
     */
    exportToText(logs) {
        return logs.map(log => {
            const timestamp = new Date(log.timestamp).toLocaleString();
            let text = `[${timestamp}] [${log.level.toUpperCase()}] ${log.message}`;
            
            if (log.data) {
                text += `\n  数据: ${JSON.stringify(log.data, null, 2)}`;
            }
            
            return text;
        }).join('\n\n');
    }
    
    /**
     * 添加监听器
     * @param {function} callback - 回调函数
     */
    on(callback) {
        this.listeners.push(callback);
    }
    
    /**
     * 移除监听器
     * @param {function} callback - 回调函数
     */
    off(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }
    
    /**
     * 通知监听器
     * @param {object} logEntry - 日志条目
     */
    notifyListeners(logEntry) {
        this.listeners.forEach(callback => {
            try {
                callback(logEntry);
            } catch (error) {
                // 使用原始console方法避免递归
                this.originalConsole.error('日志监听器错误:', error);
            }
        });
    }
    
    /**
     * 搜索日志
     * @param {string} query - 搜索查询
     * @param {object} options - 搜索选项
     * @returns {Array} 搜索结果
     */
    search(query, options = {}) {
        const searchTerm = query.toLowerCase();
        const caseSensitive = options.caseSensitive || false;
        const searchFields = options.fields || ['message'];
        
        return this.logs.filter(log => {
            return searchFields.some(field => {
                let value = log[field];
                if (field === 'data' && log.data) {
                    value = JSON.stringify(log.data);
                }
                
                if (typeof value === 'string') {
                    const searchValue = caseSensitive ? value : value.toLowerCase();
                    const searchQuery = caseSensitive ? query : searchTerm;
                    return searchValue.includes(searchQuery);
                }
                
                return false;
            });
        });
    }
}

    // 创建全局日志实例（单例模式）
    let loggerInstance = null;
    
    /**
     * @OTA_FACTORY 获取日志服务实例
     * 🏷️ 标签: @OTA_LOGGER_FACTORY
     * 📝 说明: 单例工厂函数，获取日志服务实例
     * ⚠️ 警告: 已注册，请勿重复开发
     * @returns {Logger} 日志服务实例
     */
    function getLogger() {
        if (!loggerInstance) {
            loggerInstance = new Logger();
            // 记录系统启动
            loggerInstance.log('OTA订单处理系统启动', 'info', {
                type: 'system_start',
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            });
        }
        return loggerInstance;
    }

    // 创建默认实例以保持向后兼容性
    const logger = getLogger();

    // 暴露到OTA命名空间
    window.OTA.logger = logger;
    window.OTA.getLogger = getLogger;

    // 向后兼容：暴露到全局window对象
    window.logger = logger;
    window.getLogger = getLogger;
    
    // 🔧 方案A：移除ota-registry注册，统一使用dependency-container
    // logger服务已通过service-locator.js注册到dependency-container
    // 移除重复的ota-registry注册以实现架构统一

})();