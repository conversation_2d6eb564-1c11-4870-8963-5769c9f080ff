/**
 * @TEST 核心组件单元测试套件
 * 🏷️ 标签: @CORE_COMPONENTS_TEST
 * 📝 说明: 为所有核心组件提供基础单元测试，验证基本功能正确性
 * 🎯 功能: 单元测试、功能验证、错误处理测试、性能测试
 * <AUTHOR>
 * @version 1.0.0
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.gemini = window.OTA.gemini || {};
window.OTA.gemini.tests = window.OTA.gemini.tests || {};

(function() {
    'use strict';

    /**
     * 核心组件测试套件类
     * 提供统一的测试框架和工具函数
     */
    class CoreComponentsTest {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.testResults = [];
            this.currentSuite = null;
            this.startTime = null;
            
            // 测试配置
            this.config = {
                timeout: 5000,           // 单个测试超时时间（毫秒）
                retryCount: 3,           // 失败重试次数
                verbose: true,           // 详细输出
                stopOnFirstFailure: false, // 遇到失败是否停止
                performanceThreshold: 1000 // 性能测试阈值（毫秒）
            };
            
            // 测试统计
            this.stats = {
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                skippedTests: 0,
                totalTime: 0,
                suiteResults: new Map()
            };
            
            // Mock数据
            this.mockData = this.createMockData();
            
            this.logger.log('核心组件测试套件初始化完成', 'info');
        }

        /**
         * 创建Mock测试数据
         * @returns {Object} Mock数据集合
         */
        createMockData() {
            return {
                // 测试订单文本
                orderTexts: {
                    chongDealer: `
                        Chong Dealer Order
                        Reference: CD123456789
                        Pickup: KLIA Terminal 1
                        Dropoff: Kuala Lumpur City Center
                        Date: 25-12-2024
                        Time: 14:30
                        Passenger: John Smith
                        Contact: +60123456789
                        Flight: MH123
                        Pax: 2
                        Price: RM 150
                    `,
                    
                    generic: `
                        Airport Transfer Booking
                        Booking ID: ABC123456789
                        From: Airport
                        To: Hotel
                        Date: 2024-12-25
                        Time: 2:30 PM
                        Guest: Jane Doe
                        Phone: +1234567890
                        Passengers: 3
                        Amount: $75 USD
                    `,
                    
                    chinese: `
                        接机服务订单
                        订单号: ZH987654321
                        接机地点: 吉隆坡国际机场
                        送达地点: 市中心酒店
                        日期: 2024年12月25日
                        时间: 下午2点30分
                        乘客姓名: 张三
                        联系电话: +86138888888
                        人数: 4人
                        价格: 200元人民币
                    `,
                    
                    malformed: `
                        Incomplete order...
                        Some text without proper structure
                        Random information
                    `
                },
                
                // 测试配置
                testConfigs: {
                    basic: {
                        timeout: 1000,
                        retries: 1
                    },
                    
                    performance: {
                        timeout: 5000,
                        retries: 0,
                        measurePerformance: true
                    }
                },
                
                // 预期结果
                expectedResults: {
                    chongDealer: {
                        ota_reference_number: 'CD123456789',
                        pickup_location: 'KLIA Terminal 1',
                        dropoff_location: 'Kuala Lumpur City Center',
                        passenger_name: 'John Smith',
                        passenger_count: 2,
                        price: '150'
                    },
                    
                    generic: {
                        ota_reference_number: 'ABC123456789',
                        pickup_location: 'Airport',
                        dropoff_location: 'Hotel',
                        passenger_name: 'Jane Doe',
                        passenger_count: 3
                    }
                }
            };
        }

        /**
         * 运行所有测试套件
         * @returns {Promise<Object>} 测试结果
         */
        async runAllTests() {
            this.logger.log('开始运行核心组件测试套件', 'info');
            this.startTime = Date.now();
            
            try {
                // 1. 测试OTA参考号识别引擎
                await this.testOTAReferenceEngine();
                
                // 2. 测试基础处理器
                await this.testBaseProcessor();
                
                // 3. 测试OTA渠道识别器
                await this.testOTAChannelIdentifier();
                
                // 4. 测试处理器路由系统
                await this.testProcessorRouter();
                
                // 5. 测试通用处理器
                await this.testGenericProcessor();
                
                // 6. 测试语言-OTA集成
                await this.testLanguageOTAIntegration();
                
                // 7. 测试服务注册中心
                await this.testServiceRegistry();
                
                // 8. 集成测试
                await this.testIntegration();
                
                // 生成测试报告
                return this.generateTestReport();
                
            } catch (error) {
                this.logger.logError('测试套件运行失败', error);
                return this.generateErrorReport(error);
            }
        }

        /**
         * 测试OTA参考号识别引擎
         */
        async testOTAReferenceEngine() {
            await this.runTestSuite('OTA参考号识别引擎', async () => {
                const engine = window.OTA?.gemini?.core?.getOTAReferenceEngine?.();
                
                if (!engine) {
                    throw new Error('OTA参考号识别引擎未找到');
                }
                
                // 测试基本识别功能
                await this.test('基本参考号识别', async () => {
                    const result = engine.identifyReference(this.mockData.orderTexts.chongDealer);
                    this.assert(result, '应该返回识别结果');
                    this.assert(result.reference, '应该包含参考号');
                    this.assertEqual(result.reference, 'CD123456789', '参考号应该正确');
                });
                
                // 测试平台特定识别
                await this.test('平台特定识别', async () => {
                    const result = engine.identifyReference(
                        this.mockData.orderTexts.chongDealer, 
                        'Chong Dealer'
                    );
                    this.assert(result.platform === 'Chong Dealer', '应该识别为Chong Dealer平台');
                    this.assert(result.confidence > 0.8, '置信度应该较高');
                });
                
                // 测试错误处理
                await this.test('错误处理', async () => {
                    const result = engine.identifyReference('');
                    this.assert(result, '空文本应该返回结果');
                    this.assert(result.confidence === 0, '空文本置信度应该为0');
                });
                
                // 性能测试
                await this.test('性能测试', async () => {
                    const startTime = Date.now();
                    for (let i = 0; i < 100; i++) {
                        engine.identifyReference(this.mockData.orderTexts.generic);
                    }
                    const duration = Date.now() - startTime;
                    this.assert(duration < this.config.performanceThreshold, 
                        `100次识别应该在${this.config.performanceThreshold}ms内完成`);
                });
            });
        }

        /**
         * 测试基础处理器
         */
        async testBaseProcessor() {
            await this.runTestSuite('基础处理器', async () => {
                const BaseProcessor = window.OTA?.gemini?.core?.BaseProcessor;
                
                if (!BaseProcessor) {
                    throw new Error('基础处理器类未找到');
                }
                
                const processor = new BaseProcessor();
                
                // 测试初始化
                await this.test('处理器初始化', async () => {
                    this.assert(processor, '处理器应该成功创建');
                    this.assert(typeof processor.processOrder === 'function', '应该有processOrder方法');
                });
                
                // 测试预处理
                await this.test('文本预处理', async () => {
                    const text = '  多余空格  \n\r  换行符  ';
                    const processed = processor.preprocessText ? processor.preprocessText(text) : text.trim();
                    this.assert(processed.length < text.length, '应该清理多余字符');
                });
                
                // 测试数据验证
                await this.test('数据验证', async () => {
                    const testData = {
                        pickup_location: 'Airport',
                        passenger_count: '2',
                        price: '100.50'
                    };
                    
                    const validated = processor.validateData ? processor.validateData(testData) : testData;
                    this.assert(validated, '应该返回验证结果');
                });
            });
        }

        /**
         * 测试OTA渠道识别器
         */
        async testOTAChannelIdentifier() {
            await this.runTestSuite('OTA渠道识别器', async () => {
                const identifier = window.OTA?.gemini?.core?.getOTAChannelIdentifier?.();
                
                if (!identifier) {
                    throw new Error('OTA渠道识别器未找到');
                }
                
                // 测试渠道识别
                await this.test('渠道识别', async () => {
                    const result = await identifier.identifyChannel(this.mockData.orderTexts.chongDealer);
                    this.assert(result, '应该返回识别结果');
                    this.assert(result.channel, '应该包含渠道信息');
                    this.assert(result.confidence >= 0, '置信度应该是有效数值');
                });
                
                // 测试降级处理
                await this.test('降级处理', async () => {
                    const result = await identifier.identifyChannel(this.mockData.orderTexts.malformed);
                    this.assert(result, '格式错误的文本应该返回降级结果');
                    this.assert(result.channel === 'generic' || result.fallback, '应该降级到通用处理');
                });
            });
        }

        /**
         * 测试处理器路由系统
         */
        async testProcessorRouter() {
            await this.runTestSuite('处理器路由系统', async () => {
                const router = window.OTA?.gemini?.core?.getProcessorRouter?.();
                
                if (!router) {
                    throw new Error('处理器路由系统未找到');
                }
                
                // 测试路由功能
                await this.test('处理器路由', async () => {
                    const processor = await router.routeToProcessor('generic');
                    this.assert(processor, '应该返回处理器实例');
                    this.assert(typeof processor.processOrder === 'function', '处理器应该有processOrder方法');
                });
                
                // 测试缓存功能
                await this.test('缓存功能', async () => {
                    const processor1 = await router.routeToProcessor('generic');
                    const processor2 = await router.routeToProcessor('generic');
                    // 注意：根据实现可能是同一实例或不同实例
                    this.assert(processor1 && processor2, '两次路由都应该成功');
                });
            });
        }

        /**
         * 测试通用处理器
         */
        async testGenericProcessor() {
            await this.runTestSuite('通用处理器', async () => {
                const processor = window.OTA?.gemini?.processors?.getGenericProcessor?.();
                
                if (!processor) {
                    throw new Error('通用处理器未找到');
                }
                
                // 测试订单处理
                await this.test('订单处理', async () => {
                    const result = await processor.processOrder(this.mockData.orderTexts.generic);
                    this.assert(result, '应该返回处理结果');
                    this.assert(result.success !== undefined, '应该包含成功状态');
                });
                
                // 测试字段提取
                await this.test('字段提取', async () => {
                    const result = await processor.processOrder(this.mockData.orderTexts.generic);
                    if (result.success && result.data) {
                        this.assert(result.data.pickup_location || result.data.dropoff_location, 
                            '应该提取到位置信息');
                    }
                });
                
                // 测试错误处理
                await this.test('错误处理', async () => {
                    const result = await processor.processOrder('');
                    this.assert(result, '空文本应该返回结果');
                    // 可能成功（返回默认值）或失败（返回错误信息）
                });
            });
        }

        /**
         * 测试语言-OTA集成
         */
        async testLanguageOTAIntegration() {
            await this.runTestSuite('语言-OTA集成', async () => {
                const integration = window.OTA?.gemini?.core?.getLanguageOTAIntegration?.();
                
                if (!integration) {
                    throw new Error('语言-OTA集成组件未找到');
                }
                
                // 测试语言检测
                await this.test('语言检测', async () => {
                    const result = await integration.processWithLanguageContext(
                        this.mockData.orderTexts.chinese
                    );
                    this.assert(result, '应该返回处理结果');
                    if (result.languageAnalysis) {
                        this.assert(result.languageAnalysis.detectedLanguages, '应该检测到语言');
                    }
                });
                
                // 测试多语言处理
                await this.test('多语言处理', async () => {
                    const mixedText = this.mockData.orderTexts.chinese + '\n' + this.mockData.orderTexts.generic;
                    const result = await integration.processWithLanguageContext(mixedText);
                    this.assert(result, '混合语言文本应该返回结果');
                });
            });
        }

        /**
         * 测试服务注册中心
         */
        async testServiceRegistry() {
            await this.runTestSuite('服务注册中心', async () => {
                const registry = window.OTA?.Registry;
                
                if (!registry) {
                    throw new Error('服务注册中心未找到');
                }
                
                // 测试服务注册
                await this.test('服务注册', async () => {
                    const testService = { name: 'testService', version: '1.0.0' };
                    const success = registry.registerService('testService', testService, '@TEST');
                    this.assert(success, '服务注册应该成功');
                });
                
                // 测试服务获取
                await this.test('服务获取', async () => {
                    const service = registry.getService('testService');
                    this.assert(service, '应该能获取到注册的服务');
                    this.assertEqual(service.name, 'testService', '服务名称应该正确');
                });
                
                // 测试服务注销
                await this.test('服务注销', async () => {
                    const success = registry.unregisterService('testService');
                    this.assert(success, '服务注销应该成功');
                    
                    const service = registry.getService('testService');
                    this.assert(!service, '注销后应该无法获取服务');
                });
            });
        }

        /**
         * 集成测试
         */
        async testIntegration() {
            await this.runTestSuite('集成测试', async () => {
                // 测试完整的处理流程
                await this.test('完整处理流程', async () => {
                    // 1. 识别渠道
                    const identifier = window.OTA?.gemini?.core?.getOTAChannelIdentifier?.();
                    if (identifier) {
                        const channelResult = await identifier.identifyChannel(this.mockData.orderTexts.generic);
                        this.assert(channelResult, '渠道识别应该成功');
                        
                        // 2. 路由到处理器
                        const router = window.OTA?.gemini?.core?.getProcessorRouter?.();
                        if (router) {
                            const processor = await router.routeToProcessor(channelResult.channel || 'generic');
                            this.assert(processor, '处理器路由应该成功');
                            
                            // 3. 处理订单
                            const result = await processor.processOrder(this.mockData.orderTexts.generic);
                            this.assert(result, '订单处理应该返回结果');
                        }
                    }
                });
                
                // 测试错误恢复
                await this.test('错误恢复', async () => {
                    try {
                        // 故意触发错误
                        const processor = window.OTA?.gemini?.processors?.getGenericProcessor?.();
                        if (processor) {
                            const result = await processor.processOrder(null);
                            // 应该优雅处理错误，不抛出异常
                            this.assert(result !== undefined, '错误情况应该返回结果');
                        }
                    } catch (error) {
                        // 如果抛出异常，检查是否是预期的错误类型
                        this.assert(error.message, '错误应该有描述信息');
                    }
                });
            });
        }

        /**
         * 运行测试套件
         * @param {string} suiteName - 套件名称
         * @param {Function} suiteFunction - 套件函数
         */
        async runTestSuite(suiteName, suiteFunction) {
            this.currentSuite = suiteName;
            const suiteStartTime = Date.now();
            
            this.logger.log(`开始测试套件: ${suiteName}`, 'info');
            
            try {
                await suiteFunction();
                
                const suiteDuration = Date.now() - suiteStartTime;
                this.stats.suiteResults.set(suiteName, {
                    status: 'passed',
                    duration: suiteDuration,
                    tests: this.testResults.filter(r => r.suite === suiteName).length
                });
                
                this.logger.log(`测试套件 ${suiteName} 完成 (${suiteDuration}ms)`, 'info');
                
            } catch (error) {
                const suiteDuration = Date.now() - suiteStartTime;
                this.stats.suiteResults.set(suiteName, {
                    status: 'failed',
                    duration: suiteDuration,
                    error: error.message
                });
                
                this.logger.logError(`测试套件 ${suiteName} 失败`, error);
                
                if (this.config.stopOnFirstFailure) {
                    throw error;
                }
            }
        }

        /**
         * 运行单个测试
         * @param {string} testName - 测试名称
         * @param {Function} testFunction - 测试函数
         */
        async test(testName, testFunction) {
            const testStartTime = Date.now();
            this.stats.totalTests++;
            
            try {
                await Promise.race([
                    testFunction(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('测试超时')), this.config.timeout)
                    )
                ]);
                
                const testDuration = Date.now() - testStartTime;
                this.testResults.push({
                    suite: this.currentSuite,
                    name: testName,
                    status: 'passed',
                    duration: testDuration
                });
                
                this.stats.passedTests++;
                
                if (this.config.verbose) {
                    this.logger.log(`✅ ${testName} (${testDuration}ms)`, 'info');
                }
                
            } catch (error) {
                const testDuration = Date.now() - testStartTime;
                this.testResults.push({
                    suite: this.currentSuite,
                    name: testName,
                    status: 'failed',
                    duration: testDuration,
                    error: error.message
                });
                
                this.stats.failedTests++;
                
                this.logger.logError(`❌ ${testName}`, error);
                
                if (this.config.stopOnFirstFailure) {
                    throw error;
                }
            }
        }

        /**
         * 断言函数
         * @param {*} condition - 条件
         * @param {string} message - 错误消息
         */
        assert(condition, message = '断言失败') {
            if (!condition) {
                throw new Error(message);
            }
        }

        /**
         * 相等断言
         * @param {*} actual - 实际值
         * @param {*} expected - 期望值
         * @param {string} message - 错误消息
         */
        assertEqual(actual, expected, message = '值不相等') {
            if (actual !== expected) {
                throw new Error(`${message}: 期望 ${expected}, 实际 ${actual}`);
            }
        }

        /**
         * 生成测试报告
         * @returns {Object} 测试报告
         */
        generateTestReport() {
            const totalTime = Date.now() - this.startTime;
            this.stats.totalTime = totalTime;
            
            const report = {
                summary: {
                    totalTests: this.stats.totalTests,
                    passedTests: this.stats.passedTests,
                    failedTests: this.stats.failedTests,
                    skippedTests: this.stats.skippedTests,
                    successRate: ((this.stats.passedTests / this.stats.totalTests) * 100).toFixed(2) + '%',
                    totalTime: totalTime + 'ms'
                },
                suites: Object.fromEntries(this.stats.suiteResults),
                details: this.testResults,
                timestamp: new Date().toISOString()
            };
            
            this.logger.log('测试报告生成完成', 'info', report.summary);
            
            return report;
        }

        /**
         * 生成错误报告
         * @param {Error} error - 错误对象
         * @returns {Object} 错误报告
         */
        generateErrorReport(error) {
            return {
                success: false,
                error: error.message,
                stack: error.stack,
                partialResults: this.testResults,
                timestamp: new Date().toISOString()
            };
        }
    }

    // 创建全局实例
    function getCoreComponentsTest() {
        if (!window.OTA.gemini.tests.coreComponentsTest) {
            window.OTA.gemini.tests.coreComponentsTest = new CoreComponentsTest();
        }
        return window.OTA.gemini.tests.coreComponentsTest;
    }

    // 暴露到全局命名空间
    window.OTA.gemini.tests.CoreComponentsTest = CoreComponentsTest;
    window.OTA.gemini.tests.getCoreComponentsTest = getCoreComponentsTest;

    // 向后兼容
    window.getCoreComponentsTest = getCoreComponentsTest;

    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerService('coreComponentsTest', getCoreComponentsTest, '@CORE_COMPONENTS_TEST');
        window.OTA.Registry.registerFactory('getCoreComponentsTest', getCoreComponentsTest, '@CORE_COMPONENTS_TEST_FACTORY');
    }

    console.log('✅ 核心组件单元测试套件已加载');

})();
