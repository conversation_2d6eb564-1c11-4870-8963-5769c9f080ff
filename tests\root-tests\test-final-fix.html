<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>最终修复验证测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .test-container { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 4px; 
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 最终修复验证测试</h1>
        <p>正在测试修复后的核心模块...</p>
        <div id="test-results"></div>
        
        <h2>📊 启动监控</h2>
        <div id="startup-monitor">等待应用启动...</div>
    </div>

    <script>
        const results = [];
        const startupMonitor = document.getElementById('startup-monitor');
        const testResults = document.getElementById('test-results');
        
        function addResult(message, type = 'success') {
            results.push({ message, type });
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            testResults.appendChild(div);
        }
        
        // 监控启动过程
        let startupErrors = 0;
        const originalError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('api-key-manager.js') || message.includes('multi-order-ui-manager.js')) {
                startupErrors++;
                addResult(`❌ 启动错误: ${message}`, 'error');
            }
            originalError.apply(console, args);
        };
        
        // 等待应用启动完成
        setTimeout(() => {
            startupMonitor.innerHTML = `
                <h3>启动结果</h3>
                <p><strong>错误数量: ${startupErrors}</strong></p>
                <p>应用状态: ${window.OTA?.app ? '✅ 已启动' : '❌ 未启动'}</p>
                <p>健康检查: ${startupErrors === 0 ? '✅ 通过' : '❌ 失败'}</p>
            `;
            
            // 测试核心功能
            testCoreModules();
        }, 5000);
        
        function testCoreModules() {
            addResult('开始测试核心模块...', 'warning');
            
            // 测试API密钥管理器
            try {
                const apiKeyManager = window.OTA?.apiKeyManager || window.getApiKeyManager?.();
                if (apiKeyManager) {
                    addResult('✅ API密钥管理器: 可用');
                    
                    // 测试获取密钥功能
                    const geminiKey = apiKeyManager.getApiKey('gemini');
                    if (geminiKey) {
                        addResult('✅ Gemini密钥获取: 成功');
                    } else {
                        addResult('⚠️ Gemini密钥获取: 无密钥', 'warning');
                    }
                } else {
                    addResult('❌ API密钥管理器: 不可用', 'error');
                }
            } catch (error) {
                addResult(`❌ API密钥管理器测试失败: ${error.message}`, 'error');
            }
            
            // 测试多订单UI管理器
            try {
                const multiOrderUI = window.OTA?.multiOrder?.getUIManager?.();
                if (multiOrderUI) {
                    addResult('✅ 多订单UI管理器: 可用');
                    
                    // 测试方法存在性
                    if (typeof multiOrderUI.handleQuickEditEscape === 'function') {
                        addResult('✅ handleQuickEditEscape方法: 已修复');
                    } else {
                        addResult('❌ handleQuickEditEscape方法: 仍然缺失', 'error');
                    }
                } else {
                    addResult('❌ 多订单UI管理器: 不可用', 'error');
                }
            } catch (error) {
                addResult(`❌ 多订单UI管理器测试失败: ${error.message}`, 'error');
            }
            
            // 最终总结
            setTimeout(() => {
                const errorCount = results.filter(r => r.type === 'error').length;
                const successMessage = errorCount === 0 ? 
                    '🎉 所有测试通过！修复成功！' : 
                    `⚠️ 还有 ${errorCount} 个问题需要处理`;
                    
                addResult(successMessage, errorCount === 0 ? 'success' : 'warning');
            }, 1000);
        }
    </script>
    
    <!-- 加载完整的应用模块以进行测试 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/api-key-manager.js"></script>
    <script src="js/core/ota-registry.js"></script>
    <script src="js/bootstrap/application-bootstrap.js"></script>
    <script src="js/components/multi-order/multi-order-ui-manager.js"></script>
    <script src="main.js"></script>
</body>
</html>