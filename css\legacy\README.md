# CSS Legacy Files

## 📁 目录说明

此目录包含已被模块化CSS架构替代的旧版样式文件。

## 📋 文件清单

### style-legacy.css
- **原位置**: 根目录 `style.css`
- **文件大小**: 4,454行
- **架构**: 单体式CSS文件
- **主题**: Neumorphism紫色主题 (#F75CF4)
- **状态**: 已被模块化架构替代
- **移动时间**: 2025-07-30
- **移动原因**: 文件组织规范化，根目录不应包含CSS文件

## 🔄 替代方案

旧版 `style.css` 已被以下模块化架构替代：

```
css/
├── main.css                 # 主入口文件
├── base/                    # 基础层
│   ├── variables.css        # CSS变量系统
│   ├── reset.css           # 样式重置
│   └── utilities.css       # 工具类
├── layout/                  # 布局层
├── components/              # 组件层
└── pages/                   # 页面层
```

## 📊 改进对比

| 特征 | 旧版 style.css | 新版模块化 |
|------|----------------|------------|
| 文件大小 | 4,454行 | ~2,000行总计 |
| 维护性 | 低 | 高 |
| 加载性能 | 较慢 | 优化 |
| 代码重用 | 低 | 高 |
| 主题色 | #F75CF4 | #9F299F |

## ⚠️ 重要说明

- **不要删除**: 这些文件作为备份保留，以防需要回滚
- **不要引用**: 请使用新的模块化CSS架构
- **参考价值**: 可作为样式迁移的参考

## 🔗 相关文档

- [CSS架构重构说明](../README.md)
- [项目文件组织规范](../../docs/file-organization-guide.md)

---
*创建时间: 2025-07-30*  
*维护者: OTA开发团队*
