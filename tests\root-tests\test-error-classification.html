<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误分类系统测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; font-size: 12px; }
        button { margin: 5px; padding: 8px 16px; }
        .result { margin: 10px 0; padding: 10px; background: #f9f9f9; border-left: 4px solid #007cba; }
        .error-type { font-weight: bold; color: #d63384; }
        .confidence { color: #198754; }
    </style>
</head>
<body>
    <h1>错误分类系统测试</h1>
    
    <div class="test-section">
        <h2>模块加载测试</h2>
        <div id="moduleTest"></div>
    </div>

    <div class="test-section">
        <h2>错误类型枚举测试</h2>
        <button onclick="testErrorTypes()">测试错误类型</button>
        <div id="errorTypesTest"></div>
    </div>

    <div class="test-section">
        <h2>字段类型识别测试</h2>
        <button onclick="testFieldTypes()">测试字段类型</button>
        <div id="fieldTypesTest"></div>
    </div>

    <div class="test-section">
        <h2>错误分类测试</h2>
        <button onclick="testDateTimeErrors()">日期时间错误</button>
        <button onclick="testCustomerInfoErrors()">客户信息错误</button>
        <button onclick="testLocationErrors()">位置错误</button>
        <button onclick="testNumericErrors()">数值错误</button>
        <button onclick="testPriceErrors()">价格错误</button>
        <button onclick="testFlightErrors()">航班错误</button>
        <div id="classificationTest"></div>
    </div>

    <div class="test-section">
        <h2>综合测试</h2>
        <button onclick="runComprehensiveTest()">运行综合测试</button>
        <div id="comprehensiveTest"></div>
    </div>

    <!-- 加载必要的模块 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/learning-engine/learning-config.js"></script>
    <script src="js/learning-engine/learning-storage-manager.js"></script>
    <script src="js/learning-engine/user-operation-learner.js"></script>
    <script src="js/learning-engine/error-classification-system.js"></script>

    <script>
        let classifier = null;

        // 初始化测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initializeTests, 100);
        });

        function initializeTests() {
            const moduleDiv = document.getElementById('moduleTest');
            
            try {
                // 测试模块加载
                if (window.OTA && window.OTA.errorClassificationSystem) {
                    classifier = getErrorClassificationSystem();
                    moduleDiv.innerHTML += '<div class="success">✅ 错误分类系统加载成功</div>';
                    moduleDiv.innerHTML += `<div class="info">版本: ${classifier.version}</div>`;
                } else {
                    moduleDiv.innerHTML += '<div class="error">❌ 错误分类系统加载失败</div>';
                    return;
                }

                // 测试错误类型枚举
                if (window.OTA.ErrorTypes) {
                    moduleDiv.innerHTML += '<div class="success">✅ 错误类型枚举加载成功</div>';
                    moduleDiv.innerHTML += `<div class="info">支持的错误类型: ${Object.keys(window.OTA.ErrorTypes).length} 种</div>`;
                } else {
                    moduleDiv.innerHTML += '<div class="error">❌ 错误类型枚举加载失败</div>';
                }

                // 测试字段类型定义
                if (window.OTA.FieldTypes) {
                    moduleDiv.innerHTML += '<div class="success">✅ 字段类型定义加载成功</div>';
                    moduleDiv.innerHTML += `<div class="info">支持的字段类型: ${Object.keys(window.OTA.FieldTypes).length} 种</div>`;
                } else {
                    moduleDiv.innerHTML += '<div class="error">❌ 字段类型定义加载失败</div>';
                }

            } catch (error) {
                moduleDiv.innerHTML += `<div class="error">初始化错误: ${error.message}</div>`;
                console.error('初始化错误:', error);
            }
        }

        function testErrorTypes() {
            const resultDiv = document.getElementById('errorTypesTest');
            
            try {
                const errorTypes = window.OTA.ErrorTypes;
                const typesList = Object.keys(errorTypes);
                
                resultDiv.innerHTML = `<div class="info">错误类型总数: ${typesList.length}</div>`;
                resultDiv.innerHTML += '<div class="info">错误类型列表:</div>';
                resultDiv.innerHTML += `<pre>${JSON.stringify(errorTypes, null, 2)}</pre>`;
                
                // 测试特定错误类型
                const testTypes = [
                    'TIME_FORMAT_ERROR',
                    'NAME_EXTRACTION_ERROR',
                    'LOCATION_PARSING_ERROR',
                    'PRICE_CONVERSION_ERROR'
                ];
                
                testTypes.forEach(type => {
                    if (errorTypes[type]) {
                        resultDiv.innerHTML += `<div class="success">✅ ${type}: ${errorTypes[type]}</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="error">❌ ${type} 未定义</div>`;
                    }
                });

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">错误类型测试失败: ${error.message}</div>`;
            }
        }

        function testFieldTypes() {
            const resultDiv = document.getElementById('fieldTypesTest');
            
            try {
                const fieldTypes = window.OTA.FieldTypes;
                const fieldsList = Object.keys(fieldTypes);
                
                resultDiv.innerHTML = `<div class="info">字段类型总数: ${fieldsList.length}</div>`;
                
                // 测试特定字段类型
                const testFields = ['customerName', 'pickupDate', 'pickup', 'otaPrice'];
                
                testFields.forEach(field => {
                    const fieldInfo = fieldTypes[field];
                    if (fieldInfo) {
                        resultDiv.innerHTML += `<div class="success">✅ ${field}:</div>`;
                        resultDiv.innerHTML += `<div class="info">  类型: ${fieldInfo.type}, 分类: ${fieldInfo.category}</div>`;
                        resultDiv.innerHTML += `<div class="info">  常见错误: ${fieldInfo.commonErrors.join(', ')}</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="error">❌ ${field} 未定义</div>`;
                    }
                });

            } catch (error) {
                resultDiv.innerHTML += `<div class="error">字段类型测试失败: ${error.message}</div>`;
            }
        }

        function testDateTimeErrors() {
            const resultDiv = document.getElementById('classificationTest');
            
            const testCases = [
                {
                    field: 'pickupDate',
                    originalValue: '2024/01/15',
                    correctedValue: '2024-01-15',
                    expected: 'date_format_error'
                },
                {
                    field: 'pickupTime',
                    originalValue: '2:30 PM',
                    correctedValue: '14:30',
                    expected: 'time_format_error'
                }
            ];

            resultDiv.innerHTML += '<h3>日期时间错误分类测试</h3>';
            
            testCases.forEach((testCase, index) => {
                try {
                    const result = classifier.classifyError(testCase);
                    
                    resultDiv.innerHTML += `<div class="result">
                        <div><strong>测试 ${index + 1}:</strong> ${testCase.field}</div>
                        <div>原始值: "${testCase.originalValue}" → 更正值: "${testCase.correctedValue}"</div>
                        <div class="error-type">错误类型: ${result.errorType}</div>
                        <div class="confidence">置信度: ${(result.confidence * 100).toFixed(1)}%</div>
                        <div>预期: ${testCase.expected} ${result.errorType === testCase.expected ? '✅' : '❌'}</div>
                    </div>`;
                    
                } catch (error) {
                    resultDiv.innerHTML += `<div class="error">测试 ${index + 1} 失败: ${error.message}</div>`;
                }
            });
        }

        function testCustomerInfoErrors() {
            const resultDiv = document.getElementById('classificationTest');
            
            const testCases = [
                {
                    field: 'customerEmail',
                    originalValue: 'john.doe@invalid',
                    correctedValue: '<EMAIL>',
                    expected: 'email_format_error'
                },
                {
                    field: 'customerName',
                    originalValue: 'John123',
                    correctedValue: 'John Smith',
                    expected: 'name_format_error'
                }
            ];

            resultDiv.innerHTML += '<h3>客户信息错误分类测试</h3>';
            
            testCases.forEach((testCase, index) => {
                try {
                    const result = classifier.classifyError(testCase);
                    
                    resultDiv.innerHTML += `<div class="result">
                        <div><strong>测试 ${index + 1}:</strong> ${testCase.field}</div>
                        <div>原始值: "${testCase.originalValue}" → 更正值: "${testCase.correctedValue}"</div>
                        <div class="error-type">错误类型: ${result.errorType}</div>
                        <div class="confidence">置信度: ${(result.confidence * 100).toFixed(1)}%</div>
                        <div>建议: ${result.suggestions.join('; ')}</div>
                    </div>`;
                    
                } catch (error) {
                    resultDiv.innerHTML += `<div class="error">测试 ${index + 1} 失败: ${error.message}</div>`;
                }
            });
        }

        function testLocationErrors() {
            const resultDiv = document.getElementById('classificationTest');
            
            const testCases = [
                {
                    field: 'pickup',
                    originalValue: 'KLIA',
                    correctedValue: 'Kuala Lumpur International Airport, Sepang, Malaysia',
                    expected: 'location_parsing_error'
                },
                {
                    field: 'dropoff',
                    originalValue: 'hotel',
                    correctedValue: 'Grand Hyatt Hotel, Kuala Lumpur',
                    expected: 'location_ambiguity_error'
                }
            ];

            resultDiv.innerHTML += '<h3>位置错误分类测试</h3>';
            
            testCases.forEach((testCase, index) => {
                try {
                    const result = classifier.classifyError(testCase);
                    
                    resultDiv.innerHTML += `<div class="result">
                        <div><strong>测试 ${index + 1}:</strong> ${testCase.field}</div>
                        <div>原始值: "${testCase.originalValue}" → 更正值: "${testCase.correctedValue}"</div>
                        <div class="error-type">错误类型: ${result.errorType}</div>
                        <div class="confidence">置信度: ${(result.confidence * 100).toFixed(1)}%</div>
                        <div>详情: ${JSON.stringify(result.details)}</div>
                    </div>`;
                    
                } catch (error) {
                    resultDiv.innerHTML += `<div class="error">测试 ${index + 1} 失败: ${error.message}</div>`;
                }
            });
        }

        function testNumericErrors() {
            const resultDiv = document.getElementById('classificationTest');
            
            const testCases = [
                {
                    field: 'passengerCount',
                    originalValue: 'two',
                    correctedValue: '2',
                    expected: 'passenger_count_error'
                },
                {
                    field: 'luggageCount',
                    originalValue: '3 bags',
                    correctedValue: '3',
                    expected: 'luggage_count_error'
                }
            ];

            resultDiv.innerHTML += '<h3>数值错误分类测试</h3>';
            
            testCases.forEach((testCase, index) => {
                try {
                    const result = classifier.classifyError(testCase);
                    
                    resultDiv.innerHTML += `<div class="result">
                        <div><strong>测试 ${index + 1}:</strong> ${testCase.field}</div>
                        <div>原始值: "${testCase.originalValue}" → 更正值: "${testCase.correctedValue}"</div>
                        <div class="error-type">错误类型: ${result.errorType}</div>
                        <div class="confidence">置信度: ${(result.confidence * 100).toFixed(1)}%</div>
                    </div>`;
                    
                } catch (error) {
                    resultDiv.innerHTML += `<div class="error">测试 ${index + 1} 失败: ${error.message}</div>`;
                }
            });
        }

        function testPriceErrors() {
            const resultDiv = document.getElementById('classificationTest');
            
            const testCases = [
                {
                    field: 'otaPrice',
                    originalValue: '$150',
                    correctedValue: 'USD 150',
                    expected: 'currency_recognition_error'
                },
                {
                    field: 'otaPrice',
                    originalValue: '150.5',
                    correctedValue: '645.15',
                    expected: 'price_conversion_error'
                }
            ];

            resultDiv.innerHTML += '<h3>价格错误分类测试</h3>';
            
            testCases.forEach((testCase, index) => {
                try {
                    const result = classifier.classifyError(testCase);
                    
                    resultDiv.innerHTML += `<div class="result">
                        <div><strong>测试 ${index + 1}:</strong> ${testCase.field}</div>
                        <div>原始值: "${testCase.originalValue}" → 更正值: "${testCase.correctedValue}"</div>
                        <div class="error-type">错误类型: ${result.errorType}</div>
                        <div class="confidence">置信度: ${(result.confidence * 100).toFixed(1)}%</div>
                        <div>详情: ${JSON.stringify(result.details)}</div>
                    </div>`;
                    
                } catch (error) {
                    resultDiv.innerHTML += `<div class="error">测试 ${index + 1} 失败: ${error.message}</div>`;
                }
            });
        }

        function testFlightErrors() {
            const resultDiv = document.getElementById('classificationTest');
            
            const testCases = [
                {
                    field: 'flightInfo',
                    originalValue: 'MH370',
                    correctedValue: 'MH 370',
                    expected: 'flight_number_error'
                },
                {
                    field: 'flightInfo',
                    originalValue: 'MH370',
                    correctedValue: 'SQ370',
                    expected: 'airline_recognition_error'
                }
            ];

            resultDiv.innerHTML += '<h3>航班错误分类测试</h3>';
            
            testCases.forEach((testCase, index) => {
                try {
                    const result = classifier.classifyError(testCase);
                    
                    resultDiv.innerHTML += `<div class="result">
                        <div><strong>测试 ${index + 1}:</strong> ${testCase.field}</div>
                        <div>原始值: "${testCase.originalValue}" → 更正值: "${testCase.correctedValue}"</div>
                        <div class="error-type">错误类型: ${result.errorType}</div>
                        <div class="confidence">置信度: ${(result.confidence * 100).toFixed(1)}%</div>
                        <div>详情: ${JSON.stringify(result.details)}</div>
                    </div>`;
                    
                } catch (error) {
                    resultDiv.innerHTML += `<div class="error">测试 ${index + 1} 失败: ${error.message}</div>`;
                }
            });
        }

        function runComprehensiveTest() {
            const resultDiv = document.getElementById('comprehensiveTest');
            
            resultDiv.innerHTML = '<h3>综合测试结果</h3>';
            
            try {
                // 运行所有测试
                testDateTimeErrors();
                testCustomerInfoErrors();
                testLocationErrors();
                testNumericErrors();
                testPriceErrors();
                testFlightErrors();
                
                resultDiv.innerHTML += '<div class="success">✅ 所有分类测试已完成</div>';
                resultDiv.innerHTML += '<div class="info">请查看上方各个测试部分的详细结果</div>';
                
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">综合测试失败: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
