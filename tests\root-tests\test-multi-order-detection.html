<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多订单检测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .hidden {
            display: none !important;
        }
        #multiOrderPanel {
            border: 2px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .order-card {
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            background-color: white;
        }
        #console-output {
            background-color: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        textarea {
            width: 100%;
            height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-results {
            background-color: #f0f8ff;
            border: 1px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success { color: #008000; font-weight: bold; }
        .error { color: #ff0000; font-weight: bold; }
        .warning { color: #ff6600; font-weight: bold; }
    </style>
</head>
<body>
    <h1>多订单检测测试页面</h1>
    
    <div class="test-container">
        <h2>测试输入</h2>
        <textarea id="orderInput" placeholder="在此输入订单文本...">订单1：张三 12345678901 从机场到酒店 2024-12-20 10:00 2人 1箱
订单2：李四 98765432100 从火车站到商场 2024-12-21 14:30 3人 2箱</textarea>
        <br>
        <button onclick="startTest()">开始测试</button>
        <button onclick="runAutomatedTest()">自动化测试</button>
        <button onclick="clearConsole()">清除日志</button>
    </div>

    <div class="test-container">
        <h2>测试结果摘要</h2>
        <div id="testResultsSummary" class="test-results">
            <p>点击"自动化测试"查看完整测试结果</p>
        </div>
    </div>

    <div class="test-container">
        <h2>控制台输出</h2>
        <div id="console-output"></div>
    </div>

    <div class="test-container">
        <h2>多订单面板状态</h2>
        <div id="multiOrderPanel" class="hidden">
            <h3>检测到多个订单</h3>
            <div id="multiOrderContent"></div>
        </div>
        <p>面板状态: <span id="panelStatus">隐藏</span></p>
    </div>

    <script>
        // 模拟控制台输出
        function log(message) {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function clearConsole() {
            document.getElementById('console-output').textContent = '';
        }

        // 模拟真实的多订单检测流程
        function simulateMultiOrderDetection(text) {
            log('🔄 开始统一订单分析处理...');
            log('🤖 智能分析订单类型...');
            
            // 模拟实际的多订单检测结果格式
            const mockMultiOrderResult = {
                isMultiOrder: true,
                orderCount: 2,
                confidence: 85,
                analysis: "检测到两个独立的订单信息",
                orders: [
                    {
                        rawText: "订单1：张三 12345678901 从机场到酒店 2024-12-20 10:00 2人 1箱",
                        customerName: "张三",
                        customerContact: "12345678901",
                        pickup: "机场",
                        dropoff: "酒店",
                        pickupDate: "2024-12-20",
                        pickupTime: "10:00",
                        passengerCount: 2,
                        luggageCount: 1
                    },
                    {
                        rawText: "订单2：李四 98765432100 从火车站到商场 2024-12-21 14:30 3人 2箱",
                        customerName: "李四",
                        customerContact: "98765432100",
                        pickup: "火车站",
                        dropoff: "商场",
                        pickupDate: "2024-12-21",
                        pickupTime: "14:30",
                        passengerCount: 3,
                        luggageCount: 2
                    }
                ]
            };

            log('📊 多订单检测结果:', 'info', {
                isMultiOrder: mockMultiOrderResult.isMultiOrder,
                orderCount: mockMultiOrderResult.orderCount,
                confidence: mockMultiOrderResult.confidence
            });

            if (mockMultiOrderResult.orderCount > 1) {
                log(`✅ 检测到多订单(${mockMultiOrderResult.orderCount}个)，触发多订单处理模式`);
                log(`✅ 检测到 ${mockMultiOrderResult.orderCount} 个订单`);
                
                // 模拟触发multiOrderDetected事件
                log('🔔 收到多订单检测事件（统一入口）');
                
                // 调用处理多订单检测的函数
                handleMultiOrderDetectionUnified(mockMultiOrderResult, text);
                
                return true;
            } else {
                log(`❌ 只检测到 ${mockMultiOrderResult.orderCount} 个订单，不符合多订单条件`);
                return false;
            }
        }

        // 模拟MultiOrderManager的handleMultiOrderDetectionUnified方法
        function handleMultiOrderDetectionUnified(multiOrderResult, orderText) {
            log('🔄 处理多订单检测事件（统一入口）', 'info', { 
                isMultiOrder: multiOrderResult?.isMultiOrder,
                orderCount: multiOrderResult?.orderCount || 0,
                confidence: multiOrderResult?.confidence || 0,
                hasOrders: !!multiOrderResult?.orders,
                orderLength: orderText?.length || 0 
            });

            // 🎯 增强调试：详细记录检测结果
            log('🔍 多订单检测结果详情:', 'info', {
                orderCount: multiOrderResult.orderCount,
                hasOrders: !!multiOrderResult.orders,
                ordersLength: multiOrderResult.orders?.length || 0,
                isMultiOrder: multiOrderResult.isMultiOrder,
                confidence: multiOrderResult.confidence
            });

            // 🎯 核心逻辑：根据orderCount决定处理方式
            if (multiOrderResult.orderCount > 1 && multiOrderResult.orders && multiOrderResult.orders.length > 1) {
                // 多订单模式：直接显示多订单面板
                log(`✅ 确认多订单模式，显示${multiOrderResult.orders.length}个订单的面板`);
                
                // 🔧 增强调试：检查面板元素状态
                const panel = document.getElementById('multiOrderPanel');
                log('📋 面板元素检查:', 'info', {
                    panelExists: !!panel,
                    panelClasses: panel?.className || 'N/A',
                    panelDisplay: panel?.style.display || 'default'
                });
                
                // 显示多订单面板
                showMultiOrderPanel(multiOrderResult.orders);
                
                log('🎉 多订单面板显示命令已执行');
                
            } else if (multiOrderResult.orderCount === 1) {
                log('✅ 确认单订单模式，隐藏多订单面板');
                hideMultiOrderPanel();
            } else {
                log('⚠️ 无有效订单，隐藏多订单面板');
                hideMultiOrderPanel();
            }
        }

        // 模拟真实的showMultiOrderPanel方法
        function showMultiOrderPanel(orders) {
            log('🔄 开始显示多订单浮窗面板...');
            
            const multiOrderPanel = document.getElementById('multiOrderPanel');
            if (!multiOrderPanel) {
                log('❌ 多订单面板元素不存在');
                return;
            }
            
            // 🔧 增强调试：显示前状态检查
            log('🔍 面板显示前状态检查:', 'info', {
                ordersCount: orders?.length || 0,
                panelExists: !!multiOrderPanel,
                currentClasses: multiOrderPanel.className,
                currentDisplay: multiOrderPanel.style.display,
                isHidden: multiOrderPanel.classList.contains('hidden')
            });
            
            log(`📋 准备显示${orders.length}个订单`);
            
            // 更新面板内容
            log('🔧 更新面板内容...');
            updatePanelContent(orders);
            
            // 显示浮窗面板 - 使用新的CSS类结构
            log('👁️ 显示浮窗面板...');
            multiOrderPanel.classList.remove('hidden');
            
            // 🔧 增强调试：移除hidden类后状态检查
            log('🔍 移除hidden类后状态:', 'info', {
                hasHiddenClass: multiOrderPanel.classList.contains('hidden'),
                allClasses: multiOrderPanel.className,
                computedDisplay: window.getComputedStyle(multiOrderPanel).display
            });
            
            // 触发显示动画
            requestAnimationFrame(() => {
                multiOrderPanel.style.display = 'flex';
                log('✨ 动画帧中设置display为flex');
            });
            
            // 更新状态显示
            const status = document.getElementById('panelStatus');
            status.textContent = '显示中';
            
            log('🎉 多订单面板显示成功');
            log(`🔍 最终面板状态 - hidden类: ${multiOrderPanel.classList.contains('hidden')}, display: ${getComputedStyle(multiOrderPanel).display}`);
        }

        function updatePanelContent(orders) {
            const content = document.getElementById('multiOrderContent');
            if (!content) return;
            
            // 生成订单内容
            let orderHTML = '<h4>检测到的订单列表：</h4>';
            orders.forEach((order, index) => {
                orderHTML += `
                    <div class="order-card">
                        <h4>订单 ${index + 1}</h4>
                        <p><strong>客户:</strong> ${order.customerName}</p>
                        <p><strong>联系:</strong> ${order.customerContact}</p>
                        <p><strong>路线:</strong> ${order.pickup} → ${order.dropoff}</p>
                        <p><strong>时间:</strong> ${order.pickupDate} ${order.pickupTime}</p>
                        <p><strong>乘客:</strong> ${order.passengerCount}人 ${order.luggageCount}箱</p>
                        <p><strong>原文:</strong> ${order.rawText}</p>
                    </div>
                `;
            });
            content.innerHTML = orderHTML;
        }

        function hideMultiOrderPanel() {
            const panel = document.getElementById('multiOrderPanel');
            const status = document.getElementById('panelStatus');
            
            if (panel) {
                panel.classList.add('hidden');
                panel.style.display = 'none';
            }
            
            if (status) {
                status.textContent = '隐藏';
            }
            
            log('🔒 多订单面板已隐藏');
        }

        function startTest() {
            log('=== 开始多订单检测测试 ===');
            
            const input = document.getElementById('orderInput').value;
            log(`📝 输入文本: ${input.substring(0, 50)}...`);
            
            // 重置面板状态
            const panel = document.getElementById('multiOrderPanel');
            const status = document.getElementById('panelStatus');
            panel.classList.add('hidden');
            status.textContent = '隐藏';
            
            log('🔄 已重置面板状态为隐藏');
            
            // 模拟延迟处理
            setTimeout(() => {
                const result = simulateMultiOrderDetection(input);
                log(`=== 测试完成，结果: ${result ? '成功检测多订单' : '未检测到多订单'} ===`);
            }, 1000);
        }

        // 自动化测试功能
        const testResults = {
            logs: [],
            panelStates: [],
            errors: []
        };

        // 增强日志功能，同时记录到testResults
        const originalLog = console.log;
        const enhancedLog = function(message, type = 'info', data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp: timestamp,
                message: message,
                type: type,
                data: data
            };
            
            testResults.logs.push(logEntry);
            
            const output = document.getElementById('console-output');
            output.textContent += `[${timestamp}] ${message}\n`;
            if (data) {
                output.textContent += `    数据: ${JSON.stringify(data, null, 2)}\n`;
            }
            output.scrollTop = output.scrollHeight;
            
            originalLog(message, data || '');
        };

        // 重新定义log函数使用增强版本
        function log(message, type = 'info', data = null) {
            enhancedLog(message, type, data);
        }

        function runAutomatedTest() {
            log('🚀 开始自动化测试');
            
            // 清空之前的结果
            testResults.logs = [];
            testResults.panelStates = [];
            testResults.errors = [];
            
            // 1. 记录初始状态
            const panel = document.getElementById('multiOrderPanel');
            testResults.panelStates.push({
                phase: 'initial',
                classes: panel?.className || 'not found',
                display: panel?.style.display || 'default',
                computed: panel ? getComputedStyle(panel).display : 'not found',
                hasHiddenClass: panel ? panel.classList.contains('hidden') : null
            });
            
            log('📋 初始面板状态记录完成');
            
            // 2. 设置测试数据
            const testInput = document.getElementById('orderInput').value;
            log(`📝 使用测试输入 (${testInput.length} 字符)`);
            
            // 3. 执行测试
            setTimeout(() => {
                try {
                    const result = simulateMultiOrderDetection(testInput);
                    log(`🔍 检测结果: ${result ? '成功' : '失败'}`);
                    
                    // 4. 等待处理完成后检查最终状态
                    setTimeout(() => {
                        const finalPanel = document.getElementById('multiOrderPanel');
                        testResults.panelStates.push({
                            phase: 'final',
                            classes: finalPanel?.className || 'not found',
                            display: finalPanel?.style.display || 'default',
                            computed: finalPanel ? getComputedStyle(finalPanel).display : 'not found',
                            hasHiddenClass: finalPanel ? finalPanel.classList.contains('hidden') : null
                        });
                        
                        log('📋 最终面板状态记录完成');
                        
                        // 5. 生成测试报告
                        generateTestReport();
                    }, 2000);
                    
                } catch (error) {
                    testResults.errors.push({
                        timestamp: new Date().toISOString(),
                        error: error.message,
                        stack: error.stack
                    });
                    log(`❌ 测试执行错误: ${error.message}`, 'error');
                }
            }, 1000);
        }

        function generateTestReport() {
            log('📊 生成测试报告...');
            
            const summary = {
                totalLogs: testResults.logs.length,
                panelStateChanges: testResults.panelStates.length,
                errors: testResults.errors.length,
                multiOrderDetected: testResults.logs.some(log => log.message.includes('收到多订单检测事件')),
                panelShowCommand: testResults.logs.some(log => log.message.includes('多订单面板显示命令已执行')),
                panelShown: testResults.logs.some(log => log.message.includes('多订单面板显示成功')),
                finalPanelVisible: testResults.panelStates.length > 1 ? 
                    !testResults.panelStates[testResults.panelStates.length - 1]?.hasHiddenClass : false
            };
            
            // 更新测试结果摘要显示
            const summaryDiv = document.getElementById('testResultsSummary');
            let summaryHTML = '<h3>测试结果摘要</h3>';
            
            summaryHTML += `<p>总日志条数: <span class="info">${summary.totalLogs}</span></p>`;
            summaryHTML += `<p>面板状态变化: <span class="info">${summary.panelStateChanges}</span></p>`;
            summaryHTML += `<p>错误数量: <span class="${summary.errors > 0 ? 'error' : 'success'}">${summary.errors}</span></p>`;
            summaryHTML += `<p>多订单检测成功: <span class="${summary.multiOrderDetected ? 'success' : 'error'}">${summary.multiOrderDetected ? '✅' : '❌'}</span></p>`;
            summaryHTML += `<p>面板显示命令执行: <span class="${summary.panelShowCommand ? 'success' : 'error'}">${summary.panelShowCommand ? '✅' : '❌'}</span></p>`;
            summaryHTML += `<p>面板显示成功: <span class="${summary.panelShown ? 'success' : 'error'}">${summary.panelShown ? '✅' : '❌'}</span></p>`;
            summaryHTML += `<p>最终面板可见: <span class="${summary.finalPanelVisible ? 'success' : 'error'}">${summary.finalPanelVisible ? '✅' : '❌'}</span></p>`;
            
            // 面板状态详情
            summaryHTML += '<h4>面板状态变化详情:</h4>';
            testResults.panelStates.forEach((state, index) => {
                summaryHTML += `<p><strong>${state.phase}:</strong> 
                    classes="${state.classes}", 
                    display="${state.display}", 
                    computed="${state.computed}", 
                    hidden=${state.hasHiddenClass}</p>`;
            });
            
            // 总体结论
            if (summary.multiOrderDetected && summary.panelShowCommand && summary.finalPanelVisible) {
                summaryHTML += '<p class="success">🎉 测试结论: 多订单检测和面板显示功能正常工作</p>';
                log('✅ 自动化测试通过：多订单检测和面板显示功能正常工作', 'success');
            } else {
                summaryHTML += '<p class="error">❌ 测试结论: 功能存在问题，请查看详细日志</p>';
                log('❌ 自动化测试失败：功能存在问题', 'error');
                
                if (!summary.multiOrderDetected) log('  问题: 多订单检测事件未触发', 'error');
                if (!summary.panelShowCommand) log('  问题: 面板显示命令未执行', 'error');
                if (!summary.finalPanelVisible) log('  问题: 最终面板状态不可见', 'error');
            }
            
            summaryDiv.innerHTML = summaryHTML;
            
            log('📊 测试报告生成完成');
        }

        // 页面加载完成后显示初始状态
        window.onload = function() {
            log('📱 测试页面加载完成');
            log('💡 请点击"开始测试"或"自动化测试"按钮来测试多订单检测功能');
        };
    </script>
</body>
</html>