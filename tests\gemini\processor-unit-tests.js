/**
 * @TEST_SUITE 专用处理器单元测试套件
 * 🏷️ 标签: @UNIT_TEST @PROCESSOR_TEST
 * 📝 说明: 为所有OTA专用处理器创建的单元测试套件，验证各自的专用处理逻辑正确性
 * 🎯 目标: 确保每个处理器的参考号识别、字段映射、预设值应用等功能正常工作
 */

(function() {
    'use strict';
    
    // 延迟获取依赖，确保加载顺序
    
    function getRegistry() {
        return window.OTA.Registry;
    }
    
    /**
     * 专用处理器单元测试类
     * 为所有OTA专用处理器提供统一的测试框架
     */
    class ProcessorUnitTests {
        constructor() {
            this.logger = window.OTA.getService('logger');
            this.testResults = new Map();
            this.processors = new Map();
            this.testData = new Map();
            
            this.logger.log('🧪 专用处理器单元测试套件初始化', 'info');
        }
        
        /**
         * 初始化测试套件
         * @returns {Promise<void>}
         */
        async initialize() {
            try {
                // 加载所有处理器
                await this.loadProcessors();
                
                // 加载测试数据
                this.loadTestData();
                
                this.logger.log('✅ 测试套件初始化完成', 'info');
            } catch (error) {
                this.logger.logError('❌ 测试套件初始化失败', error);
                throw error;
            }
        }
        
        /**
         * 加载所有处理器
         * @private
         */
        async loadProcessors() {
            const registry = getRegistry();
            if (!registry) {
                throw new Error('服务注册中心未初始化');
            }
            
            // 加载所有专用处理器
            const processorNames = [
                'fliggy-processor',
                'jrcoach-processor', 
                'kkday-processor',
                'klook-processor',
                'ctrip-processor',
                'agoda-processor',
                'booking-processor'
            ];
            
            for (const name of processorNames) {
                try {
                    const processor = registry.getService(name);
                    if (processor) {
                        this.processors.set(name, processor);
                        this.logger.log(`✅ 加载处理器: ${name}`, 'info');
                    } else {
                        this.logger.log(`⚠️ 处理器未找到: ${name}`, 'warning');
                    }
                } catch (error) {
                    this.logger.logError(`❌ 加载处理器失败: ${name}`, error);
                }
            }
        }
        
        /**
         * 加载测试数据
         * @private
         */
        loadTestData() {
            // Fliggy测试数据
            this.testData.set('fliggy-processor', {
                validOrders: [
                    {
                        text: '飞猪旅行订单 订单号：FL123456789 联系人：张三 电话：13800138000 出发地：浦东机场 目的地：市区酒店 日期：2024-01-15 时间：10:30',
                        expected: {
                            platform: 'fliggy',
                            order_reference: 'FL123456789',
                            customer_name: '张三',
                            phone: '13800138000',
                            pickup_location: '浦东机场',
                            destination: '市区酒店'
                        }
                    }
                ],
                invalidOrders: [
                    '这不是一个有效的订单信息',
                    ''
                ]
            });
            
            // KKday测试数据
            this.testData.set('kkday-processor', {
                validOrders: [
                    {
                        text: 'KKday订单确认 订单编号：KK987654321 预订人：李四 联系电话：13900139000 接送地点：桃园机场 目的地：台北车站 出发日期：2024-01-20 出发时间：14:00',
                        expected: {
                            platform: 'kkday',
                            order_reference: 'KK987654321',
                            customer_name: '李四',
                            phone: '13900139000',
                            pickup_location: '桃园机场',
                            destination: '台北车站'
                        }
                    }
                ],
                invalidOrders: [
                    '无效订单内容',
                    'random text without order info'
                ]
            });
            
            // Klook测试数据
            this.testData.set('klook-processor', {
                validOrders: [
                    {
                        text: 'Klook客路体验 确认号：KL456789123 旅客姓名：王五 手机号码：13700137000 集合地点：香港机场 目的地：迪士尼乐园 体验日期：2024-01-25 集合时间：09:00',
                        expected: {
                            platform: 'klook',
                            order_reference: 'KL456789123',
                            customer_name: '王五',
                            phone: '13700137000',
                            pickup_location: '香港机场',
                            destination: '迪士尼乐园'
                        }
                    }
                ],
                invalidOrders: [
                    '不完整的订单信息',
                    'incomplete order data'
                ]
            });
            
            // Ctrip测试数据
            this.testData.set('ctrip-processor', {
                validOrders: [
                    {
                        text: '携程用车订单 产品订单号：CT789123456 联系人：赵六 联系电话：13600136000 上车地点：首都机场T3 下车地点：北京饭店 用车日期：2024-01-30 用车时间：16:30',
                        expected: {
                            platform: 'ctrip',
                            order_reference: 'CT789123456',
                            customer_name: '赵六',
                            phone: '13600136000',
                            pickup_location: '首都机场T3',
                            destination: '北京饭店'
                        }
                    }
                ],
                invalidOrders: [
                    '错误的订单格式',
                    'wrong format'
                ]
            });
            
            // Agoda测试数据
            this.testData.set('agoda-processor', {
                validOrders: [
                    {
                        text: 'Agoda Booking Confirmation Number: AG123789456 Guest Name: John Smith Phone: +60123456789 Pickup Location: KLIA Airport Destination: Kuala Lumpur Hotel Check-in Date: 25-01-2024 Check-in Time: 18:00',
                        expected: {
                            platform: 'agoda',
                            order_reference: 'AG123789456',
                            customer_name: 'John Smith',
                            phone: '+60123456789',
                            pickup_location: 'KLIA Airport',
                            destination: 'Kuala Lumpur Hotel'
                        }
                    }
                ],
                invalidOrders: [
                    'Invalid booking information',
                    'no valid data here'
                ]
            });
            
            // Booking.com测试数据
            this.testData.set('booking-processor', {
                validOrders: [
                    {
                        text: 'Booking.com Confirmation Number: BK987321654 Main Guest: Jane Doe Contact Number: +65987654321 Property Address: Marina Bay Sands Singapore Destination: Changi Airport Check-in Date: 28-01-2024 Check-in Time: 11:00',
                        expected: {
                            platform: 'booking',
                            order_reference: 'BK987321654',
                            customer_name: 'Jane Doe',
                            phone: '+65987654321',
                            pickup_location: 'Marina Bay Sands Singapore',
                            destination: 'Changi Airport'
                        }
                    }
                ],
                invalidOrders: [
                    'Not a valid booking',
                    'empty content'
                ]
            });
        }
        
        /**
         * 运行所有测试
         * @returns {Promise<Object>} 测试结果
         */
        async runAllTests() {
            this.logger.log('🚀 开始运行所有处理器单元测试', 'info');
            
            const results = {
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                processorResults: new Map(),
                startTime: Date.now()
            };
            
            for (const [processorName, processor] of this.processors) {
                try {
                    const processorResult = await this.testProcessor(processorName, processor);
                    results.processorResults.set(processorName, processorResult);
                    results.totalTests += processorResult.totalTests;
                    results.passedTests += processorResult.passedTests;
                    results.failedTests += processorResult.failedTests;
                } catch (error) {
                    this.logger.logError(`处理器测试失败: ${processorName}`, error);
                    results.failedTests++;
                }
            }
            
            results.endTime = Date.now();
            results.duration = results.endTime - results.startTime;
            results.successRate = results.totalTests > 0 ? (results.passedTests / results.totalTests * 100).toFixed(2) : 0;
            
            this.logger.log(`✅ 测试完成 - 总计: ${results.totalTests}, 通过: ${results.passedTests}, 失败: ${results.failedTests}, 成功率: ${results.successRate}%`, 'info');
            
            return results;
        }
        
        /**
         * 测试单个处理器
         * @param {string} processorName - 处理器名称
         * @param {Object} processor - 处理器实例
         * @returns {Promise<Object>} 测试结果
         * @private
         */
        async testProcessor(processorName, processor) {
            this.logger.log(`🔍 测试处理器: ${processorName}`, 'info');
            
            const result = {
                processorName,
                totalTests: 0,
                passedTests: 0,
                failedTests: 0,
                tests: []
            };
            
            const testData = this.testData.get(processorName);
            if (!testData) {
                this.logger.log(`⚠️ 没有找到测试数据: ${processorName}`, 'warning');
                return result;
            }
            
            // 测试参考号识别
            for (const orderData of testData.validOrders) {
                result.totalTests++;
                try {
                    const referenceResult = await processor.identifyReferenceNumber(orderData.text);
                    if (referenceResult.success && referenceResult.referenceNumber === orderData.expected.order_reference) {
                        result.passedTests++;
                        result.tests.push({
                            name: `参考号识别 - ${orderData.expected.order_reference}`,
                            status: 'PASS',
                            expected: orderData.expected.order_reference,
                            actual: referenceResult.referenceNumber
                        });
                    } else {
                        result.failedTests++;
                        result.tests.push({
                            name: `参考号识别 - ${orderData.expected.order_reference}`,
                            status: 'FAIL',
                            expected: orderData.expected.order_reference,
                            actual: referenceResult.referenceNumber,
                            error: '参考号识别不匹配'
                        });
                    }
                } catch (error) {
                    result.failedTests++;
                    result.tests.push({
                        name: `参考号识别 - ${orderData.expected.order_reference}`,
                        status: 'ERROR',
                        error: error.message
                    });
                }
            }
            
            // 测试订单处理
            for (const orderData of testData.validOrders) {
                result.totalTests++;
                try {
                    const processResult = await processor.processOrder(orderData.text);
                    if (processResult.success) {
                        const data = processResult.data;
                        let passed = true;
                        const checks = [];
                        
                        // 检查关键字段
                        for (const [key, expectedValue] of Object.entries(orderData.expected)) {
                            if (data[key] !== expectedValue) {
                                passed = false;
                                checks.push(`${key}: 期望 ${expectedValue}, 实际 ${data[key]}`);
                            }
                        }
                        
                        if (passed) {
                            result.passedTests++;
                            result.tests.push({
                                name: `订单处理 - ${orderData.expected.order_reference}`,
                                status: 'PASS'
                            });
                        } else {
                            result.failedTests++;
                            result.tests.push({
                                name: `订单处理 - ${orderData.expected.order_reference}`,
                                status: 'FAIL',
                                error: `字段不匹配: ${checks.join(', ')}`
                            });
                        }
                    } else {
                        result.failedTests++;
                        result.tests.push({
                            name: `订单处理 - ${orderData.expected.order_reference}`,
                            status: 'FAIL',
                            error: processResult.error || '处理失败'
                        });
                    }
                } catch (error) {
                    result.failedTests++;
                    result.tests.push({
                        name: `订单处理 - ${orderData.expected.order_reference}`,
                        status: 'ERROR',
                        error: error.message
                    });
                }
            }
            
            // 测试无效订单处理
            for (const invalidOrder of testData.invalidOrders) {
                result.totalTests++;
                try {
                    const processResult = await processor.processOrder(invalidOrder);
                    // 无效订单应该返回失败或者空结果
                    if (!processResult.success || !processResult.data || !processResult.data.order_reference) {
                        result.passedTests++;
                        result.tests.push({
                            name: `无效订单处理 - ${invalidOrder.substring(0, 20)}...`,
                            status: 'PASS'
                        });
                    } else {
                        result.failedTests++;
                        result.tests.push({
                            name: `无效订单处理 - ${invalidOrder.substring(0, 20)}...`,
                            status: 'FAIL',
                            error: '应该拒绝无效订单但却成功处理了'
                        });
                    }
                } catch (error) {
                    // 抛出异常也是可以接受的
                    result.passedTests++;
                    result.tests.push({
                        name: `无效订单处理 - ${invalidOrder.substring(0, 20)}...`,
                        status: 'PASS',
                        note: '正确抛出异常'
                    });
                }
            }
            
            // 测试状态获取
            result.totalTests++;
            try {
                const status = processor.getStatus();
                if (status && status.platform && status.initialized) {
                    result.passedTests++;
                    result.tests.push({
                        name: '状态获取',
                        status: 'PASS'
                    });
                } else {
                    result.failedTests++;
                    result.tests.push({
                        name: '状态获取',
                        status: 'FAIL',
                        error: '状态信息不完整'
                    });
                }
            } catch (error) {
                result.failedTests++;
                result.tests.push({
                    name: '状态获取',
                    status: 'ERROR',
                    error: error.message
                });
            }
            
            const successRate = result.totalTests > 0 ? (result.passedTests / result.totalTests * 100).toFixed(2) : 0;
            this.logger.log(`📊 ${processorName} 测试结果: ${result.passedTests}/${result.totalTests} (${successRate}%)`, 'info');
            
            return result;
        }
        
        /**
         * 生成测试报告
         * @param {Object} results - 测试结果
         * @returns {string} 测试报告
         */
        generateReport(results) {
            let report = `
# 专用处理器单元测试报告

## 总体结果
- 总测试数: ${results.totalTests}
- 通过测试: ${results.passedTests}
- 失败测试: ${results.failedTests}
- 成功率: ${results.successRate}%
- 测试时长: ${results.duration}ms

## 各处理器详细结果

`;
            
            for (const [processorName, processorResult] of results.processorResults) {
                const successRate = processorResult.totalTests > 0 ? 
                    (processorResult.passedTests / processorResult.totalTests * 100).toFixed(2) : 0;
                
                report += `### ${processorName}
- 测试数: ${processorResult.totalTests}
- 通过: ${processorResult.passedTests}
- 失败: ${processorResult.failedTests}
- 成功率: ${successRate}%

`;
                
                // 添加失败的测试详情
                const failedTests = processorResult.tests.filter(test => test.status === 'FAIL' || test.status === 'ERROR');
                if (failedTests.length > 0) {
                    report += `#### 失败的测试:\n`;
                    for (const test of failedTests) {
                        report += `- ${test.name}: ${test.error}\n`;
                    }
                    report += '\n';
                }
            }
            
            return report;
        }
        
        /**
         * 运行特定处理器的测试
         * @param {string} processorName - 处理器名称
         * @returns {Promise<Object>} 测试结果
         */
        async runProcessorTest(processorName) {
            const processor = this.processors.get(processorName);
            if (!processor) {
                throw new Error(`处理器不存在: ${processorName}`);
            }
            
            return await this.testProcessor(processorName, processor);
        }
        
        /**
         * 获取测试统计信息
         * @returns {Object} 统计信息
         */
        getTestStats() {
            return {
                availableProcessors: Array.from(this.processors.keys()),
                testDataLoaded: Array.from(this.testData.keys()),
                totalProcessors: this.processors.size,
                totalTestData: this.testData.size
            };
        }
    }
    
    // 创建全局测试实例
    const processorUnitTests = new ProcessorUnitTests();
    
    // 注册到全局
    if (typeof window !== 'undefined') {
        window.OTA = window.OTA || {};
        window.OTA.processorUnitTests = processorUnitTests;
        
        // 注册到服务注册中心
        if (window.OTA.Registry) {
            window.OTA.Registry.registerService('processor-unit-tests', processorUnitTests, {
                dependencies: ['logger'],
                description: '专用处理器单元测试套件，验证所有OTA处理器的功能正确性'
            });
        }
    }
    
    // Node.js环境支持
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = ProcessorUnitTests;
    }

    // 自动运行测试的便捷函数
    window.runProcessorTests = async function() {
        try {
            await processorUnitTests.initialize();
            const results = await processorUnitTests.runAllTests();
            const report = processorUnitTests.generateReport(results);

            console.log('📋 测试报告:');
            console.log(report);

            return results;
        } catch (error) {
            console.error('❌ 测试运行失败:', error);
            throw error;
        }
    };

    // 运行特定处理器测试的便捷函数
    window.runSingleProcessorTest = async function(processorName) {
        try {
            await processorUnitTests.initialize();
            const result = await processorUnitTests.runProcessorTest(processorName);

            console.log(`📊 ${processorName} 测试结果:`, result);

            return result;
        } catch (error) {
            console.error(`❌ ${processorName} 测试失败:`, error);
            throw error;
        }
    };

})();
