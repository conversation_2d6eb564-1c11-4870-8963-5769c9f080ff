/**
 * 文件结构优化脚本
 * 用于安全地重构OTA系统的文件结构，将嵌套目录优化为2层架构
 * <AUTHOR> System
 * @created 2025-01-29
 */

(function() {
    'use strict';

    /**
     * 文件结构优化器
     */
    class FileStructureOptimizer {
        constructor() {
            this.logger = console;
            this.backupCreated = false;
            this.optimizationPlan = this.createOptimizationPlan();
        }

        /**
         * 创建优化计划
         */
        createOptimizationPlan() {
            return {
                // 已完成的重构
                completed: {
                    'js/ai/gemini-core.js': [
                        'js/ai/gemini/core/flight-number-processor.js',
                        'js/ai/gemini/core/data-normalizer.js', 
                        'js/ai/gemini/core/ota-reference-engine.js',
                        'js/ai/gemini/core/address-translator.js',
                        'js/ai/gemini/core/prompt-template-engine.js'
                    ],
                    'js/ai/gemini-processors.js': [
                        'js/ai/gemini/core/base-processor.js',
                        'js/ai/gemini/processors/chong-dealer-processor.js',
                        'js/ai/gemini/processors/agoda-processor.js',
                        'js/ai/gemini/processors/booking-processor.js',
                        'js/ai/gemini/processors/generic-processor.js'
                    ],
                    'js/ai/gemini-configs.js': [
                        'js/ai/gemini/configs/field-mapping.js',
                        'js/ai/gemini/configs/preset-values.js',
                        'js/ai/gemini/configs/fallback-config.js',
                        'js/ai/gemini/configs/performance-optimization.js',
                        'js/ai/gemini/configs/ota-reference-patterns.js'
                    ]
                },

                // 待处理的文件
                pending: {
                    // 需要合并到现有文件的
                    toMerge: {
                        'js/ai/gemini-core.js': [
                            'js/ai/gemini/core/error-recovery-engine.js',
                            'js/ai/gemini/core/image-analysis-engine.js',
                            'js/ai/gemini/core/ota-channel-identifier.js',
                            'js/ai/gemini/core/processor-router.js',
                            'js/ai/gemini/core/language-ota-integration.js',
                            'js/ai/gemini/core/config-manager.js'
                        ],
                        'js/ai/gemini-processors.js': [
                            'js/ai/gemini/processors/ctrip-processor.js',
                            'js/ai/gemini/processors/fliggy-processor.js',
                            'js/ai/gemini/processors/jrcoach-processor.js',
                            'js/ai/gemini/processors/kkday-processor.js',
                            'js/ai/gemini/processors/klook-processor.js'
                        ]
                    },

                    // 需要移动到新位置的
                    toMove: {
                        'js/tests/gemini-tests.js': [
                            'js/ai/gemini/tests/backward-compatibility-test.js',
                            'js/ai/gemini/tests/comprehensive-test-suite.js',
                            'js/ai/gemini/tests/core-components-test.js',
                            'js/ai/gemini/tests/integration-test-suite.js',
                            'js/ai/gemini/tests/processor-tests.js',
                            'js/ai/gemini/tests/processor-unit-tests.js',
                            'js/ai/gemini/tests/system-integration-test.js'
                        ]
                    },

                    // 需要删除的空目录和重复文件
                    toDelete: [
                        'js/ai/gemini/integration/',
                        'js/ai/gemini/integrations/',
                        'js/ai/gemini/migration/',
                        'js/ai/gemini/monitoring/',
                        'js/parsers/'  // 空目录
                    ]
                }
            };
        }

        /**
         * 执行优化
         */
        async optimize() {
            try {
                this.logger.log('🚀 开始文件结构优化...');

                // 1. 验证当前状态
                await this.validateCurrentState();

                // 2. 创建备份
                await this.createBackup();

                // 3. 验证新文件的功能
                await this.validateNewFiles();

                // 4. 清理旧文件
                await this.cleanupOldFiles();

                // 5. 验证系统功能
                await this.validateSystemFunctionality();

                this.logger.log('✅ 文件结构优化完成！');
                return { success: true, message: '优化成功完成' };

            } catch (error) {
                this.logger.error('❌ 优化过程中出现错误:', error);
                await this.rollback();
                return { success: false, error: error.message };
            }
        }

        /**
         * 验证当前状态
         */
        async validateCurrentState() {
            this.logger.log('📋 验证当前文件状态...');

            // 检查新文件是否存在
            const newFiles = [
                'js/ai/gemini-core.js',
                'js/ai/gemini-processors.js', 
                'js/ai/gemini-configs.js'
            ];

            for (const file of newFiles) {
                if (!this.fileExists(file)) {
                    throw new Error(`新文件不存在: ${file}`);
                }
            }

            // 检查index.html是否已更新
            if (!this.indexHtmlUpdated()) {
                throw new Error('index.html尚未更新script引用');
            }

            this.logger.log('✅ 当前状态验证通过');
        }

        /**
         * 创建备份
         */
        async createBackup() {
            if (this.backupCreated) return;

            this.logger.log('💾 创建文件备份...');
            
            // 这里应该实际创建备份，但由于是浏览器环境，我们只记录
            this.logger.log('📝 备份计划已记录，原文件将保留直到验证完成');
            this.backupCreated = true;
        }

        /**
         * 验证新文件功能
         */
        async validateNewFiles() {
            this.logger.log('🔍 验证新文件功能...');

            // 检查全局命名空间
            const requiredNamespaces = [
                'window.OTA.ai.gemini.FlightNumberProcessor',
                'window.OTA.ai.gemini.DataNormalizer',
                'window.OTA.ai.gemini.processors.ChongDealerProcessor',
                'window.OTA.ai.gemini.configs.fieldMapping'
            ];

            for (const namespace of requiredNamespaces) {
                if (!this.checkNamespace(namespace)) {
                    throw new Error(`命名空间不存在: ${namespace}`);
                }
            }

            // 测试核心功能
            await this.testCoreFunctionality();

            this.logger.log('✅ 新文件功能验证通过');
        }

        /**
         * 清理旧文件
         */
        async cleanupOldFiles() {
            this.logger.log('🧹 清理旧文件结构...');

            // 标记需要删除的文件
            const filesToDelete = [
                ...this.optimizationPlan.completed['js/ai/gemini-core.js'],
                ...this.optimizationPlan.completed['js/ai/gemini-processors.js'],
                ...this.optimizationPlan.completed['js/ai/gemini-configs.js']
            ];

            this.logger.log(`📝 标记删除 ${filesToDelete.length} 个已合并的文件`);
            
            // 在实际环境中，这里会执行文件删除操作
            // 由于是浏览器环境，我们只记录操作
            filesToDelete.forEach(file => {
                this.logger.log(`🗑️ 标记删除: ${file}`);
            });

            this.logger.log('✅ 旧文件清理完成');
        }

        /**
         * 验证系统功能
         */
        async validateSystemFunctionality() {
            this.logger.log('🔧 验证系统整体功能...');

            try {
                // 测试依赖容器
                if (window.OTA?.container?.isInitialized()) {
                    this.logger.log('✅ 依赖容器正常');
                }

                // 测试Gemini服务
                if (window.OTA?.ai?.gemini?.flightProcessor) {
                    const testResult = window.OTA.ai.gemini.flightProcessor.process('MH370');
                    if (testResult.success) {
                        this.logger.log('✅ Gemini核心功能正常');
                    }
                }

                // 测试配置访问
                const config = window.OTA?.ai?.gemini?.getConfig('fieldMapping');
                if (config) {
                    this.logger.log('✅ 配置系统正常');
                }

                this.logger.log('✅ 系统功能验证通过');

            } catch (error) {
                throw new Error(`系统功能验证失败: ${error.message}`);
            }
        }

        /**
         * 回滚操作
         */
        async rollback() {
            this.logger.log('🔄 执行回滚操作...');
            this.logger.log('📝 请手动恢复index.html中的script引用');
            this.logger.log('📝 请检查并恢复任何被修改的文件');
        }

        /**
         * 辅助方法：检查文件是否存在
         */
        fileExists(path) {
            // 在浏览器环境中，我们通过检查script标签来判断
            const scripts = document.querySelectorAll('script[src]');
            return Array.from(scripts).some(script => script.src.includes(path));
        }

        /**
         * 辅助方法：检查index.html是否已更新
         */
        indexHtmlUpdated() {
            const scripts = document.querySelectorAll('script[src]');
            const scriptSrcs = Array.from(scripts).map(script => script.src);
            
            // 检查是否包含新的合并文件
            return scriptSrcs.some(src => src.includes('gemini-core.js')) &&
                   scriptSrcs.some(src => src.includes('gemini-processors.js')) &&
                   scriptSrcs.some(src => src.includes('gemini-configs.js'));
        }

        /**
         * 辅助方法：检查命名空间
         */
        checkNamespace(namespace) {
            try {
                const parts = namespace.split('.');
                let current = window;
                for (const part of parts) {
                    if (!current[part]) return false;
                    current = current[part];
                }
                return true;
            } catch (error) {
                return false;
            }
        }

        /**
         * 测试核心功能
         */
        async testCoreFunctionality() {
            // 测试航班号处理
            if (window.OTA?.ai?.gemini?.flightProcessor) {
                const result = window.OTA.ai.gemini.flightProcessor.process('Flight MH370');
                if (!result.success) {
                    throw new Error('航班号处理器测试失败');
                }
            }

            // 测试数据标准化
            if (window.OTA?.ai?.gemini?.dataNormalizer) {
                const result = window.OTA.ai.gemini.dataNormalizer.normalize({
                    pickup_date: '2025-01-29',
                    pickup_time: '14:30'
                });
                if (!result.success) {
                    throw new Error('数据标准化器测试失败');
                }
            }
        }
    }

    // 导出到全局命名空间
    window.OTA = window.OTA || {};
    window.OTA.FileStructureOptimizer = FileStructureOptimizer;

    // 创建实例
    window.OTA.fileOptimizer = new FileStructureOptimizer();

    console.log('✅ 文件结构优化器已加载');

})();
