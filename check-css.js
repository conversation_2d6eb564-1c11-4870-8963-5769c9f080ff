const fs = require('fs');

function checkCSSBraces(filePath) {
    try {
        const css = fs.readFileSync(filePath, 'utf8');
        const lines = css.split('\n');
        let braceStack = [];
        let inComment = false;
        let inString = false;
        let stringChar = '';
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            
            for (let j = 0; j < line.length; j++) {
                const char = line[j];
                const prevChar = j > 0 ? line[j-1] : '';
                const nextChar = j < line.length - 1 ? line[j+1] : '';
                
                // 处理注释
                if (!inString && char === '/' && nextChar === '*') {
                    inComment = true;
                    j++;
                    continue;
                }
                
                if (inComment && char === '*' && nextChar === '/') {
                    inComment = false;
                    j++;
                    continue;
                }
                
                if (inComment) continue;
                
                // 处理字符串
                if (!inString && (char === '"' || char === "'")) {
                    inString = true;
                    stringChar = char;
                    continue;
                }
                
                if (inString && char === stringChar && prevChar !== '\\') {
                    inString = false;
                    stringChar = '';
                    continue;
                }
                
                if (inString) continue;
                
                // 检查括号
                if (char === '{') {
                    braceStack.push({
                        type: 'brace',
                        line: i + 1,
                        char: j + 1,
                        context: line.trim()
                    });
                } else if (char === '}') {
                    if (braceStack.length === 0) {
                        console.log(`❌ 错误：第 ${i + 1} 行第 ${j + 1} 列 - 意外的闭合括号 }`);
                        console.log(`   行内容: ${line.trim()}`);
                        return false;
                    } else {
                        braceStack.pop();
                    }
                }
            }
        }
        
        if (braceStack.length > 0) {
            console.log(`❌ 错误：发现 ${braceStack.length} 个未闭合的括号:`);
            braceStack.forEach(brace => {
                console.log(`   第 ${brace.line} 行第 ${brace.char} 列: ${brace.context}`);
            });
            return false;
        }
        
        console.log('✅ CSS 语法检查通过 - 所有括号匹配正确');
        return true;
        
    } catch (error) {
        console.error('❌ 文件读取错误:', error.message);
        return false;
    }
}

// 检查文件
const result = checkCSSBraces('css/legacy/style-legacy.css');
process.exit(result ? 0 : 1);
