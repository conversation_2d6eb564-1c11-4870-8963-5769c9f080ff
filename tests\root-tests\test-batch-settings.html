<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量设置界面测试</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/language-dropdown.css">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .preview-note {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 批量设置界面预览测试</h1>
        
        <div class="preview-note">
            <strong>📝 测试说明：</strong><br>
            1. 语言设置已改为下拉菜单+多选择tick box方式<br>
            2. 已移除服务类型和车辆选择设置<br>
            3. 点击语言下拉菜单测试多选功能<br>
            4. 此为静态预览，不会实际应用设置
        </div>

        <!-- 批量设置面板预览 -->
        <div id="batchControlPreview">
            <!-- 这里将通过JavaScript插入批量设置面板 -->
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/managers/form-manager.js"></script>
    <script src="js/managers/price-manager.js"></script>
    <script src="js/managers/event-manager.js"></script>
    <script src="js/managers/state-manager.js"></script>
    <script src="js/managers/realtime-analysis-manager.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/multi-order-manager.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 开始批量设置界面测试');

            // 等待模块加载完成
            setTimeout(() => {
                if (window.OTA && window.OTA.multiOrderManager) {
                    const manager = window.OTA.multiOrderManager;
                    
                    try {
                        // 生成批量控制面板HTML
                        const batchControlHTML = manager.generateBatchControlPanel();
                        
                        // 插入到预览容器中
                        document.getElementById('batchControlPreview').innerHTML = batchControlHTML;
                        
                        console.log('✅ 批量设置面板已成功生成');
                        
                        // 测试功能按钮点击事件
                        const testButtons = document.querySelectorAll('.batch-apply-btn');
                        testButtons.forEach(btn => {
                            btn.addEventListener('click', function(e) {
                                e.preventDefault();
                                alert('测试环境：按钮功能已触发，但不会实际应用设置');
                            });
                        });
                        
                        // 添加成功提示
                        const successDiv = document.createElement('div');
                        successDiv.innerHTML = `
                            <div style="margin-top: 20px; padding: 15px; background: #d4edda; color: #155724; border-radius: 5px; border: 1px solid #c3e6cb;">
                                <strong>✅ 测试成功！</strong><br>
                                • 语言设置已更改为下拉菜单+多选择tick box方式<br>
                                • 服务类型批量设置已移除<br>
                                • 车辆选择批量设置已移除<br>
                                • 点击语言下拉菜单可以测试多选功能
                            </div>
                        `;
                        document.querySelector('.test-container').appendChild(successDiv);
                        
                    } catch (error) {
                        console.error('🧪 批量设置界面测试失败:', error);
                        document.getElementById('batchControlPreview').innerHTML = `
                            <div style="padding: 20px; background: #f8d7da; color: #721c24; border-radius: 5px;">
                                ❌ 批量设置面板生成失败: ${error.message}
                            </div>
                        `;
                    }
                } else {
                    console.log('🧪 多订单管理器未加载');
                    document.getElementById('batchControlPreview').innerHTML = `
                        <div style="padding: 20px; background: #fff3cd; color: #856404; border-radius: 5px;">
                            ⚠️ 多订单管理器未加载，请检查脚本加载顺序
                        </div>
                    `;
                }
            }, 1500);
        });
    </script>
</body>
</html>