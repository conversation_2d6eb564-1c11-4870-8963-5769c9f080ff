/**
 * @TEST_SUITE 系统集成测试
 * 🏷️ 标签: @INTEGRATION_TEST @SYSTEM_VALIDATION
 * 📝 说明: 验证新的模块化Gemini架构与现有系统的完整集成
 * 🎯 目标: 确保所有组件正确协作，向后兼容性完整
 */

(function() {
    'use strict';
    
    // 确保OTA命名空间存在
    window.OTA = window.OTA || {};
    window.OTA.gemini = window.OTA.gemini || {};
    window.OTA.gemini.tests = window.OTA.gemini.tests || {};
    
    /**
     * 系统集成测试套件
     */
    class SystemIntegrationTest {
        constructor() {
            this.logger = window.getLogger?.() || console;
            this.testResults = [];
            this.testConfig = {
                timeout: 10000,
                retryCount: 3,
                verbose: true
            };
        }
        
        /**
         * 运行所有集成测试
         * @returns {Promise<Object>} 测试结果
         */
        async runAllTests() {
            this.logger.log('开始系统集成测试', 'info');
            this.testResults = [];
            
            const tests = [
                { name: '服务实例测试', method: this.testServiceInstances },
                { name: '全局函数测试', method: this.testGlobalFunctions },
                { name: '服务定位器集成测试', method: this.testServiceLocatorIntegration },
                { name: 'Multi-Order Manager集成测试', method: this.testMultiOrderManagerIntegration },
                { name: 'Form Manager集成测试', method: this.testFormManagerIntegration },
                { name: 'UI Manager集成测试', method: this.testUIManagerIntegration },
                { name: 'Main.js集成测试', method: this.testMainJsIntegration },
                { name: '向后兼容性测试', method: this.testBackwardCompatibility },
                { name: '端到端功能测试', method: this.testEndToEndFunctionality }
            ];
            
            for (const test of tests) {
                try {
                    const result = await this.runSingleTest(test.name, test.method.bind(this));
                    this.testResults.push(result);
                } catch (error) {
                    this.testResults.push({
                        name: test.name,
                        passed: false,
                        error: error.message,
                        timestamp: new Date().toISOString()
                    });
                }
            }
            
            const summary = this.generateTestSummary();
            this.logger.log('系统集成测试完成', 'info', summary);
            
            return {
                summary,
                results: this.testResults,
                passed: summary.passed === summary.total
            };
        }
        
        /**
         * 运行单个测试
         * @param {string} testName - 测试名称
         * @param {Function} testMethod - 测试方法
         * @returns {Promise<Object>} 测试结果
         */
        async runSingleTest(testName, testMethod) {
            const startTime = Date.now();
            
            try {
                const result = await Promise.race([
                    testMethod(),
                    new Promise((_, reject) => 
                        setTimeout(() => reject(new Error('测试超时')), this.testConfig.timeout)
                    )
                ]);
                
                const duration = Date.now() - startTime;
                
                if (this.testConfig.verbose) {
                    this.logger.log(`✅ ${testName} 通过 (${duration}ms)`, 'success');
                }
                
                return {
                    name: testName,
                    passed: true,
                    duration,
                    result,
                    timestamp: new Date().toISOString()
                };
                
            } catch (error) {
                const duration = Date.now() - startTime;
                
                this.logger.logError(`❌ ${testName} 失败 (${duration}ms)`, error);
                
                return {
                    name: testName,
                    passed: false,
                    duration,
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            }
        }
        
        /**
         * 测试服务实例
         */
        async testServiceInstances() {
            const checks = [];
            
            // 检查Gemini服务实例
            checks.push({
                name: 'window.OTA.geminiService存在',
                condition: !!window.OTA.geminiService,
                value: !!window.OTA.geminiService
            });
            
            checks.push({
                name: 'Gemini服务有parseOrder方法',
                condition: !!(window.OTA.geminiService?.parseOrder),
                value: typeof window.OTA.geminiService?.parseOrder
            });
            
            checks.push({
                name: 'Gemini服务有isAvailable方法',
                condition: !!(window.OTA.geminiService?.isAvailable),
                value: typeof window.OTA.geminiService?.isAvailable
            });
            
            // 检查协调器实例
            checks.push({
                name: '协调器实例存在',
                condition: !!(window.OTA?.gemini?.coordinator || window.OTA?.gemini?.getGeminiCoordinator?.()),
                value: !!(window.OTA?.gemini?.coordinator)
            });
            
            const failedChecks = checks.filter(check => !check.condition);
            if (failedChecks.length > 0) {
                throw new Error(`服务实例检查失败: ${failedChecks.map(c => c.name).join(', ')}`);
            }
            
            return { checks, allPassed: true };
        }
        
        /**
         * 测试全局函数
         */
        async testGlobalFunctions() {
            const functions = [
                'getGeminiService',
                'getGeminiCoordinator', 
                'parseOrderWithGemini'
            ];
            
            const results = {};
            
            for (const funcName of functions) {
                const func = window[funcName];
                results[funcName] = {
                    exists: typeof func === 'function',
                    callable: false,
                    result: null
                };
                
                if (typeof func === 'function') {
                    try {
                        // 对于getGeminiService和getGeminiCoordinator，直接调用
                        if (funcName.startsWith('get')) {
                            const result = func();
                            results[funcName].callable = true;
                            results[funcName].result = !!result;
                        } else {
                            // parseOrderWithGemini需要参数，只检查是否存在
                            results[funcName].callable = true;
                        }
                    } catch (error) {
                        results[funcName].error = error.message;
                    }
                }
            }
            
            const failedFunctions = Object.entries(results)
                .filter(([name, result]) => !result.exists)
                .map(([name]) => name);
                
            if (failedFunctions.length > 0) {
                throw new Error(`全局函数缺失: ${failedFunctions.join(', ')}`);
            }
            
            return results;
        }
        
        /**
         * 测试服务定位器集成
         */
        async testServiceLocatorIntegration() {
            const checks = [];
            
            // 检查服务定位器存在
            checks.push({
                name: '服务定位器存在',
                condition: !!window.OTA.serviceLocator,
                value: !!window.OTA.serviceLocator
            });
            
            // 检查可以通过getGeminiService获取服务
            const geminiService = window.getGeminiService?.();
            checks.push({
                name: 'getGeminiService返回有效实例',
                condition: !!geminiService,
                value: !!geminiService
            });
            
            // 检查返回的服务有必要方法
            checks.push({
                name: '返回的服务有parseOrder方法',
                condition: !!(geminiService?.parseOrder),
                value: typeof geminiService?.parseOrder
            });
            
            const failedChecks = checks.filter(check => !check.condition);
            if (failedChecks.length > 0) {
                throw new Error(`服务定位器集成失败: ${failedChecks.map(c => c.name).join(', ')}`);
            }
            
            return { checks, allPassed: true };
        }
        
        /**
         * 测试Multi-Order Manager集成
         */
        async testMultiOrderManagerIntegration() {
            // 检查multi-order-manager是否能正确获取Gemini服务
            const multiOrderManager = window.OTA?.multiOrderManager;
            
            if (!multiOrderManager) {
                throw new Error('Multi-Order Manager不存在');
            }
            
            // 检查getService方法
            if (typeof multiOrderManager.getService !== 'function') {
                throw new Error('Multi-Order Manager缺少getService方法');
            }
            
            // 尝试获取Gemini服务
            const geminiService = multiOrderManager.getService('gemini');
            if (!geminiService) {
                throw new Error('Multi-Order Manager无法获取Gemini服务');
            }
            
            // 检查服务方法
            if (typeof geminiService.parseOrder !== 'function') {
                throw new Error('Multi-Order Manager获取的Gemini服务缺少parseOrder方法');
            }
            
            return {
                multiOrderManagerExists: true,
                canGetGeminiService: true,
                geminiServiceValid: true
            };
        }
        
        /**
         * 测试Form Manager集成
         */
        async testFormManagerIntegration() {
            // 检查window.OTA.geminiService是否存在且有getLanguagesIdArray方法
            const geminiService = window.OTA?.geminiService;
            
            if (!geminiService) {
                throw new Error('window.OTA.geminiService不存在');
            }
            
            if (typeof geminiService.getLanguagesIdArray !== 'function') {
                throw new Error('Gemini服务缺少getLanguagesIdArray方法');
            }
            
            // 测试语言检测功能
            const testText = '您好，我需要接机服务';
            const testName = '张三';
            
            try {
                const languages = geminiService.getLanguagesIdArray(testText, testName);
                if (!Array.isArray(languages)) {
                    throw new Error('getLanguagesIdArray应该返回数组');
                }
            } catch (error) {
                throw new Error(`语言检测功能测试失败: ${error.message}`);
            }
            
            return {
                geminiServiceExists: true,
                hasLanguageMethod: true,
                languageDetectionWorks: true
            };
        }
        
        /**
         * 测试UI Manager集成
         */
        async testUIManagerIntegration() {
            // 检查UI Manager是否能正确显示Gemini状态
            const uiManager = window.getUIManager?.();
            
            if (!uiManager) {
                throw new Error('UI Manager不存在');
            }
            
            // 检查Gemini状态元素
            const geminiStatusElement = document.getElementById('geminiStatus');
            if (!geminiStatusElement) {
                throw new Error('Gemini状态显示元素不存在');
            }
            
            return {
                uiManagerExists: true,
                geminiStatusElementExists: true
            };
        }
        
        /**
         * 测试Main.js集成
         */
        async testMainJsIntegration() {
            // 检查main.js是否能正确检查Gemini服务
            const geminiService = window.getGeminiService?.();
            
            if (!geminiService) {
                throw new Error('main.js无法获取Gemini服务');
            }
            
            // 检查isAvailable方法
            if (typeof geminiService.isAvailable !== 'function') {
                throw new Error('Gemini服务缺少isAvailable方法');
            }
            
            // 测试可用性检查
            const isAvailable = geminiService.isAvailable();
            
            return {
                canGetGeminiService: true,
                hasIsAvailableMethod: true,
                availabilityCheck: isAvailable
            };
        }
        
        /**
         * 测试向后兼容性
         */
        async testBackwardCompatibility() {
            const compatibilityChecks = [];
            
            // 检查旧的全局接口
            const oldInterfaces = [
                'window.parseOrderWithGemini',
                'window.GeminiService',
                'window.OTA.geminiService'
            ];
            
            for (const interfacePath of oldInterfaces) {
                const parts = interfacePath.split('.');
                let obj = window;
                let exists = true;
                
                for (let i = 1; i < parts.length; i++) {
                    if (obj && typeof obj === 'object' && parts[i] in obj) {
                        obj = obj[parts[i]];
                    } else {
                        exists = false;
                        break;
                    }
                }
                
                compatibilityChecks.push({
                    interface: interfacePath,
                    exists,
                    type: typeof obj
                });
            }
            
            const missingInterfaces = compatibilityChecks.filter(check => !check.exists);
            if (missingInterfaces.length > 0) {
                throw new Error(`向后兼容接口缺失: ${missingInterfaces.map(c => c.interface).join(', ')}`);
            }
            
            return { compatibilityChecks, allCompatible: true };
        }
        
        /**
         * 测试端到端功能
         */
        async testEndToEndFunctionality() {
            // 简单的端到端测试：尝试解析一个测试订单
            const testOrderText = '接机服务\n客户：张三\n电话：13800138000\n航班：CA1234\n时间：2024-01-15 10:30';
            
            try {
                const geminiService = window.getGeminiService();
                if (!geminiService) {
                    throw new Error('无法获取Gemini服务');
                }
                
                // 注意：这里不实际调用parseOrder，因为可能需要API密钥
                // 只检查方法是否存在且可调用
                if (typeof geminiService.parseOrder !== 'function') {
                    throw new Error('parseOrder方法不存在');
                }
                
                return {
                    geminiServiceAccessible: true,
                    parseOrderMethodExists: true,
                    readyForActualParsing: true
                };
                
            } catch (error) {
                throw new Error(`端到端功能测试失败: ${error.message}`);
            }
        }
        
        /**
         * 生成测试摘要
         * @returns {Object} 测试摘要
         */
        generateTestSummary() {
            const total = this.testResults.length;
            const passed = this.testResults.filter(r => r.passed).length;
            const failed = total - passed;
            const totalDuration = this.testResults.reduce((sum, r) => sum + (r.duration || 0), 0);
            
            return {
                total,
                passed,
                failed,
                passRate: total > 0 ? (passed / total * 100).toFixed(2) + '%' : '0%',
                totalDuration: totalDuration + 'ms',
                timestamp: new Date().toISOString()
            };
        }
        
        /**
         * 获取详细测试报告
         * @returns {Object} 详细报告
         */
        getDetailedReport() {
            return {
                summary: this.generateTestSummary(),
                results: this.testResults,
                config: this.testConfig,
                environment: {
                    userAgent: navigator.userAgent,
                    timestamp: new Date().toISOString(),
                    otaNamespaceExists: !!window.OTA,
                    geminiModulesLoaded: !!(window.OTA?.gemini)
                }
            };
        }
    }
    
    // 创建全局实例
    const systemIntegrationTest = new SystemIntegrationTest();
    
    // 注册到全局命名空间
    window.OTA.gemini.tests.SystemIntegrationTest = SystemIntegrationTest;
    window.OTA.gemini.tests.systemIntegrationTest = systemIntegrationTest;
    
    // 便捷访问函数
    window.OTA.gemini.tests.runIntegrationTests = function() {
        return systemIntegrationTest.runAllTests();
    };
    
    window.OTA.gemini.tests.getIntegrationReport = function() {
        return systemIntegrationTest.getDetailedReport();
    };
    
    // 自动运行测试（延迟执行以确保所有模块加载完成）
    if (typeof window !== 'undefined' && window.location) {
        setTimeout(async () => {
            try {
                console.log('🧪 开始自动系统集成测试...');
                const results = await systemIntegrationTest.runAllTests();
                
                if (results.passed) {
                    console.log('✅ 所有系统集成测试通过！', results.summary);
                } else {
                    console.warn('⚠️ 部分系统集成测试失败', results.summary);
                    console.log('详细结果:', results.results);
                }
            } catch (error) {
                console.error('❌ 系统集成测试执行失败:', error);
            }
        }, 3000); // 3秒延迟确保所有模块加载完成
    }
    
    console.log('✅ 系统集成测试模块已加载');
    
})();
