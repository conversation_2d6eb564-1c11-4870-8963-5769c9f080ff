<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务注册验证测试</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5; 
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .test-section h3 { 
            margin-top: 0; 
            color: #333; 
        }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 4px; 
        }
        .success { 
            background-color: #d4edda; 
            border: 1px solid #c3e6cb; 
            color: #155724; 
        }
        .error { 
            background-color: #f8d7da; 
            border: 1px solid #f5c6cb; 
            color: #721c24; 
        }
        .warning { 
            background-color: #fff3cd; 
            border: 1px solid #ffeaa7; 
            color: #856404; 
        }
        .info { 
            background-color: #d1ecf1; 
            border: 1px solid #bee5eb; 
            color: #0c5460; 
        }
        button { 
            background: #007bff; 
            color: white; 
            padding: 10px 20px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin: 5px; 
        }
        button:hover { 
            background: #0056b3; 
        }
        .summary { 
            font-weight: bold; 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 5px; 
        }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 4px; 
            overflow-x: auto; 
            white-space: pre-wrap; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 服务注册验证测试</h1>
        <p><strong>目的</strong>: 验证新增的6个服务注册是否正常工作</p>
        
        <div class="test-section">
            <h3>📋 测试概览</h3>
            <div id="overview">
                <p>等待测试开始...</p>
            </div>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div class="test-section">
            <h3>🧪 服务注册验证测试</h3>
            <div id="service-registration-tests">
                <p>点击"运行所有测试"开始验证</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔄 服务降级机制测试</h3>
            <div id="fallback-tests">
                <p>点击"运行所有测试"开始验证</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 测试总结</h3>
            <div id="test-summary">
                <p>等待测试完成...</p>
            </div>
        </div>
    </div>

    <!-- 引入依赖文件 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/managers/currency-converter.js"></script>

    <script>
        // 测试结果存储
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0,
            details: []
        };

        // 新增的6个服务列表
        const newServices = [
            'imageUploadManager',
            'currencyConverter', 
            'multiOrderManager',
            'orderHistoryManager',
            'pagingServiceManager',
            'i18nManager'
        ];

        // 所有应该注册的服务
        const allExpectedServices = [
            'appState', 'logger', 'apiService', 'geminiService', 'uiManager', 
            'utils', 'formManager', 'languageManager', 'kimiService',
            ...newServices
        ];

        function logResult(test, status, message, details = null) {
            testResults.total++;
            testResults.details.push({
                test: test,
                status: status,
                message: message,
                details: details,
                timestamp: new Date().toISOString()
            });

            if (status === 'success') testResults.passed++;
            else if (status === 'error') testResults.failed++;
            else if (status === 'warning') testResults.warnings++;

            console.log(`[${status.toUpperCase()}] ${test}: ${message}`, details || '');
        }

        function displayResult(containerId, test, status, message, details = null) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${status}`;
            
            let content = `<strong>${test}</strong>: ${message}`;
            if (details) {
                content += `<pre>${JSON.stringify(details, null, 2)}</pre>`;
            }
            
            resultDiv.innerHTML = content;
            container.appendChild(resultDiv);
        }

        function testServiceRegistration() {
            const container = document.getElementById('service-registration-tests');
            container.innerHTML = '<h4>正在测试服务注册...</h4>';

            // 检查OTA命名空间
            if (!window.OTA) {
                logResult('OTA命名空间', 'error', 'window.OTA 不存在');
                displayResult('service-registration-tests', 'OTA命名空间', 'error', 'window.OTA 不存在');
                return;
            }

            // 检查服务定位器
            if (!window.OTA.serviceLocator) {
                logResult('服务定位器', 'error', 'window.OTA.serviceLocator 不存在');
                displayResult('service-registration-tests', '服务定位器', 'error', 'window.OTA.serviceLocator 不存在');
                return;
            }

            // 检查依赖容器
            if (!window.OTA.container) {
                logResult('依赖容器', 'error', 'window.OTA.container 不存在');
                displayResult('service-registration-tests', '依赖容器', 'error', 'window.OTA.container 不存在');
                return;
            }

            logResult('基础组件', 'success', '所有基础组件存在');
            displayResult('service-registration-tests', '基础组件', 'success', '所有基础组件存在');

            // 测试每个新增服务的注册
            newServices.forEach(serviceName => {
                try {
                    // 检查是否已注册到依赖容器
                    const isRegistered = window.OTA.container.has(serviceName);
                    
                    if (isRegistered) {
                        logResult(`${serviceName}注册`, 'success', '已成功注册到依赖容器');
                        displayResult('service-registration-tests', `${serviceName}注册`, 'success', '已成功注册到依赖容器');
                        
                        // 尝试获取服务实例
                        try {
                            const service = window.OTA.getService(serviceName);
                            if (service) {
                                logResult(`${serviceName}获取`, 'success', '可以成功获取服务实例', {
                                    hasIsAvailable: typeof service.isAvailable === 'function',
                                    isAvailable: service.isAvailable ? service.isAvailable() : 'N/A'
                                });
                                displayResult('service-registration-tests', `${serviceName}获取`, 'success', '可以成功获取服务实例');
                            } else {
                                logResult(`${serviceName}获取`, 'warning', '获取到null服务实例');
                                displayResult('service-registration-tests', `${serviceName}获取`, 'warning', '获取到null服务实例');
                            }
                        } catch (getError) {
                            logResult(`${serviceName}获取`, 'error', '获取服务实例失败', getError.message);
                            displayResult('service-registration-tests', `${serviceName}获取`, 'error', `获取服务实例失败: ${getError.message}`);
                        }
                    } else {
                        logResult(`${serviceName}注册`, 'error', '未注册到依赖容器');
                        displayResult('service-registration-tests', `${serviceName}注册`, 'error', '未注册到依赖容器');
                    }
                } catch (error) {
                    logResult(`${serviceName}测试`, 'error', '测试失败', error.message);
                    displayResult('service-registration-tests', `${serviceName}测试`, 'error', `测试失败: ${error.message}`);
                }
            });
        }

        function testFallbackMechanism() {
            const container = document.getElementById('fallback-tests');
            container.innerHTML = '<h4>正在测试降级机制...</h4>';

            newServices.forEach(serviceName => {
                try {
                    // 测试统一服务访问
                    const service1 = window.OTA.getService(serviceName);
                    
                    // 测试旧的全局函数（如果存在）
                    const oldFunctionName = `get${serviceName.charAt(0).toUpperCase() + serviceName.slice(1)}`;
                    let service2 = null;
                    
                    if (window[oldFunctionName]) {
                        try {
                            service2 = window[oldFunctionName]();
                        } catch (error) {
                            // 忽略废弃函数的错误
                        }
                    }

                    if (service1) {
                        logResult(`${serviceName}统一访问`, 'success', '通过window.OTA.getService()可以访问');
                        displayResult('fallback-tests', `${serviceName}统一访问`, 'success', '通过window.OTA.getService()可以访问');
                        
                        if (service2 && service1 === service2) {
                            logResult(`${serviceName}一致性`, 'success', '新旧访问方式返回相同实例');
                            displayResult('fallback-tests', `${serviceName}一致性`, 'success', '新旧访问方式返回相同实例');
                        }
                    } else {
                        logResult(`${serviceName}统一访问`, 'warning', '无法通过统一访问获取服务');
                        displayResult('fallback-tests', `${serviceName}统一访问`, 'warning', '无法通过统一访问获取服务');
                    }
                } catch (error) {
                    logResult(`${serviceName}降级测试`, 'error', '降级机制测试失败', error.message);
                    displayResult('fallback-tests', `${serviceName}降级测试`, 'error', `降级机制测试失败: ${error.message}`);
                }
            });
        }

        function testServiceAvailability() {
            // 获取所有可用服务
            const availableServices = window.OTA.serviceLocator.getAvailableServices();
            
            logResult('可用服务数量', 'info', `共发现 ${availableServices.length} 个服务`, availableServices);
            
            // 检查预期服务是否都可用
            const missingServices = allExpectedServices.filter(service => !availableServices.includes(service));
            
            if (missingServices.length === 0) {
                logResult('服务完整性', 'success', '所有预期服务都可用');
                displayResult('service-registration-tests', '服务完整性', 'success', '所有预期服务都可用');
            } else {
                logResult('服务完整性', 'warning', '发现缺失服务', missingServices);
                displayResult('service-registration-tests', '服务完整性', 'warning', `发现缺失服务: ${missingServices.join(', ')}`);
            }
        }

        function updateSummary() {
            const summaryContainer = document.getElementById('test-summary');
            const overviewContainer = document.getElementById('overview');
            
            const passRate = testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0;
            const status = passRate >= 80 ? 'success' : passRate >= 60 ? 'warning' : 'error';
            
            const summaryHTML = `
                <div class="summary ${status}">
                    <h4>测试结果总览</h4>
                    <p><strong>总测试数</strong>: ${testResults.total}</p>
                    <p><strong>通过</strong>: ${testResults.passed}</p>
                    <p><strong>失败</strong>: ${testResults.failed}</p>
                    <p><strong>警告</strong>: ${testResults.warnings}</p>
                    <p><strong>通过率</strong>: ${passRate}%</p>
                </div>
            `;
            
            summaryContainer.innerHTML = summaryHTML;
            
            const overviewHTML = `
                <div class="result ${status}">
                    <strong>当前状态</strong>: ${passRate >= 80 ? '✅ 良好' : passRate >= 60 ? '⚠️ 需要注意' : '❌ 需要修复'}
                    (${testResults.passed}/${testResults.total} 通过)
                </div>
            `;
            
            overviewContainer.innerHTML = overviewHTML;
        }

        function runAllTests() {
            // 重置结果
            testResults = {
                total: 0,
                passed: 0,
                failed: 0,
                warnings: 0,
                details: []
            };

            // 清除之前的结果
            document.getElementById('service-registration-tests').innerHTML = '';
            document.getElementById('fallback-tests').innerHTML = '';

            console.log('🚀 开始服务注册验证测试...');

            // 依次运行所有测试
            setTimeout(() => {
                testServiceRegistration();
                
                setTimeout(() => {
                    testServiceAvailability();
                    
                    setTimeout(() => {
                        testFallbackMechanism();
                        
                        setTimeout(() => {
                            updateSummary();
                            console.log('✅ 所有测试完成', testResults);
                        }, 500);
                    }, 500);
                }, 500);
            }, 100);
        }

        function clearResults() {
            document.getElementById('service-registration-tests').innerHTML = '<p>点击"运行所有测试"开始验证</p>';
            document.getElementById('fallback-tests').innerHTML = '<p>点击"运行所有测试"开始验证</p>';
            document.getElementById('test-summary').innerHTML = '<p>等待测试完成...</p>';
            document.getElementById('overview').innerHTML = '<p>等待测试开始...</p>';
            
            testResults = {
                total: 0,
                passed: 0,
                failed: 0,
                warnings: 0,
                details: []
            };
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            console.log('服务注册验证测试页面已加载');
            
            // 检查基础依赖是否加载
            setTimeout(() => {
                if (window.OTA && window.OTA.serviceLocator) {
                    document.getElementById('overview').innerHTML = `
                        <div class="result success">
                            <strong>状态</strong>: ✅ 基础组件已加载，可以开始测试
                        </div>
                    `;
                } else {
                    document.getElementById('overview').innerHTML = `
                        <div class="result error">
                            <strong>状态</strong>: ❌ 基础组件未加载，请检查依赖文件
                        </div>
                    `;
                }
            }, 1000);
        });
    </script>
</body>
</html>