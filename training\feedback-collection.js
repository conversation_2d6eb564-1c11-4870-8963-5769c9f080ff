/**
 * 智能学习型格式预处理引擎 - 用户反馈收集系统
 * 负责收集用户反馈、问题报告、功能建议和培训评价
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-16
 */

class FeedbackCollectionSystem {
    constructor() {
        this.version = '1.0.0';
        this.feedbackData = [];
        this.currentRatings = {};
        
        this.initialize();
    }

    /**
     * 初始化反馈收集系统
     */
    initialize() {
        this.setupEventListeners();
        this.populateSystemInfo();
        this.loadExistingFeedback();
        
        console.log('反馈收集系统已初始化', { version: this.version });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 表单提交事件
        document.getElementById('general-feedback-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitGeneralFeedback(e.target);
        });

        document.getElementById('bug-report-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitBugReport(e.target);
        });

        document.getElementById('feature-request-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitFeatureRequest(e.target);
        });

        document.getElementById('training-feedback-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitTrainingFeedback(e.target);
        });

        // 评分星星点击事件
        document.querySelectorAll('.rating-star').forEach(star => {
            star.addEventListener('click', (e) => {
                this.handleRatingClick(e.target);
            });

            star.addEventListener('mouseover', (e) => {
                this.handleRatingHover(e.target);
            });
        });

        // 评分区域鼠标离开事件
        document.querySelectorAll('.rating-group').forEach(group => {
            group.addEventListener('mouseleave', () => {
                this.resetRatingDisplay(group);
            });
        });
    }

    /**
     * 填充系统信息
     */
    populateSystemInfo() {
        // 浏览器信息
        const browserInfo = this.getBrowserInfo();
        document.getElementById('browser-info').value = browserInfo;

        // 操作系统信息
        const osInfo = this.getOSInfo();
        document.getElementById('os-info').value = osInfo;

        // 屏幕分辨率
        const screenInfo = `${screen.width}x${screen.height}`;
        document.getElementById('screen-info').value = screenInfo;
    }

    /**
     * 获取浏览器信息
     */
    getBrowserInfo() {
        const userAgent = navigator.userAgent;
        let browserName = 'Unknown';
        let browserVersion = 'Unknown';

        if (userAgent.indexOf('Chrome') > -1) {
            browserName = 'Chrome';
            browserVersion = userAgent.match(/Chrome\/([0-9.]+)/)?.[1] || 'Unknown';
        } else if (userAgent.indexOf('Firefox') > -1) {
            browserName = 'Firefox';
            browserVersion = userAgent.match(/Firefox\/([0-9.]+)/)?.[1] || 'Unknown';
        } else if (userAgent.indexOf('Safari') > -1) {
            browserName = 'Safari';
            browserVersion = userAgent.match(/Version\/([0-9.]+)/)?.[1] || 'Unknown';
        } else if (userAgent.indexOf('Edge') > -1) {
            browserName = 'Edge';
            browserVersion = userAgent.match(/Edge\/([0-9.]+)/)?.[1] || 'Unknown';
        }

        return `${browserName} ${browserVersion}`;
    }

    /**
     * 获取操作系统信息
     */
    getOSInfo() {
        const userAgent = navigator.userAgent;
        const platform = navigator.platform;

        if (userAgent.indexOf('Windows') > -1) {
            return 'Windows';
        } else if (userAgent.indexOf('Mac') > -1) {
            return 'macOS';
        } else if (userAgent.indexOf('Linux') > -1) {
            return 'Linux';
        } else if (userAgent.indexOf('Android') > -1) {
            return 'Android';
        } else if (userAgent.indexOf('iOS') > -1) {
            return 'iOS';
        }

        return platform || 'Unknown';
    }

    /**
     * 处理评分点击
     */
    handleRatingClick(star) {
        const rating = parseInt(star.dataset.rating);
        const field = star.dataset.field || 'overall';
        const ratingGroup = star.parentElement;
        
        // 更新当前评分
        this.currentRatings[field] = rating;
        
        // 更新隐藏输入字段
        const hiddenInput = ratingGroup.parentElement.querySelector('input[type="hidden"]');
        if (hiddenInput) {
            hiddenInput.value = rating;
        }
        
        // 更新星星显示
        this.updateRatingDisplay(ratingGroup, rating);
    }

    /**
     * 处理评分悬停
     */
    handleRatingHover(star) {
        const rating = parseInt(star.dataset.rating);
        const ratingGroup = star.parentElement;
        
        this.updateRatingDisplay(ratingGroup, rating);
    }

    /**
     * 更新评分显示
     */
    updateRatingDisplay(ratingGroup, rating) {
        const stars = ratingGroup.querySelectorAll('.rating-star');
        
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });
    }

    /**
     * 重置评分显示
     */
    resetRatingDisplay(ratingGroup) {
        const field = ratingGroup.querySelector('.rating-star').dataset.field || 'overall';
        const currentRating = this.currentRatings[field] || 0;
        
        this.updateRatingDisplay(ratingGroup, currentRating);
    }

    /**
     * 提交一般反馈
     */
    submitGeneralFeedback(form) {
        const formData = new FormData(form);
        const feedback = {
            type: 'general',
            timestamp: new Date().toISOString(),
            data: {
                name: formData.get('name'),
                department: formData.get('department'),
                experience: formData.get('experience'),
                overall_rating: parseInt(formData.get('overall_rating')) || 0,
                usability_rating: parseInt(formData.get('usability_rating')) || 0,
                learning_rating: parseInt(formData.get('learning_rating')) || 0,
                liked_features: formData.getAll('liked_features'),
                improvements: formData.get('improvements'),
                additional_comments: formData.get('additional_comments')
            }
        };

        this.saveFeedback(feedback);
        this.showSuccessMessage();
        form.reset();
        this.currentRatings = {};
    }

    /**
     * 提交问题报告
     */
    submitBugReport(form) {
        const formData = new FormData(form);
        const bugReport = {
            type: 'bug_report',
            timestamp: new Date().toISOString(),
            data: {
                bug_type: formData.get('bug_type'),
                severity: formData.get('severity'),
                bug_description: formData.get('bug_description'),
                reproduction_steps: formData.get('reproduction_steps'),
                expected_result: formData.get('expected_result'),
                browser: formData.get('browser'),
                os: formData.get('os'),
                screen_resolution: formData.get('screen_resolution')
            }
        };

        this.saveFeedback(bugReport);
        this.showSuccessMessage();
        form.reset();
    }

    /**
     * 提交功能建议
     */
    submitFeatureRequest(form) {
        const formData = new FormData(form);
        const featureRequest = {
            type: 'feature_request',
            timestamp: new Date().toISOString(),
            data: {
                feature_type: formData.get('feature_type'),
                priority: formData.get('priority'),
                feature_description: formData.get('feature_description'),
                use_case: formData.get('use_case'),
                expected_benefit: formData.get('expected_benefit')
            }
        };

        this.saveFeedback(featureRequest);
        this.showSuccessMessage();
        form.reset();
    }

    /**
     * 提交培训反馈
     */
    submitTrainingFeedback(form) {
        const formData = new FormData(form);
        const trainingFeedback = {
            type: 'training_feedback',
            timestamp: new Date().toISOString(),
            data: {
                training_content_rating: parseInt(formData.get('training_content_rating')) || 0,
                trainer_rating: parseInt(formData.get('trainer_rating')) || 0,
                materials_rating: parseInt(formData.get('materials_rating')) || 0,
                training_duration: formData.get('training_duration'),
                independence_level: formData.get('independence_level'),
                training_improvements: formData.get('training_improvements')
            }
        };

        this.saveFeedback(trainingFeedback);
        this.showSuccessMessage();
        form.reset();
        this.currentRatings = {};
    }

    /**
     * 保存反馈数据
     */
    saveFeedback(feedback) {
        try {
            // 添加到内存数组
            this.feedbackData.push(feedback);
            
            // 保存到本地存储
            const existingFeedback = JSON.parse(localStorage.getItem('ota_user_feedback') || '[]');
            existingFeedback.push(feedback);
            localStorage.setItem('ota_user_feedback', JSON.stringify(existingFeedback));
            
            // 如果有学习系统存储管理器，也保存一份
            if (window.OTA && window.OTA.learningStorageManager) {
                window.OTA.learningStorageManager.setData('user_feedback', existingFeedback);
            }
            
            console.log('反馈已保存:', feedback);
            
            // 发送到服务器（如果配置了）
            this.sendFeedbackToServer(feedback);
            
        } catch (error) {
            console.error('保存反馈失败:', error);
            alert('保存反馈时出现错误，请稍后重试。');
        }
    }

    /**
     * 发送反馈到服务器
     */
    sendFeedbackToServer(feedback) {
        // 这里可以配置服务器端点
        const serverEndpoint = '/api/feedback'; // 需要配置实际的服务器地址
        
        // 如果没有配置服务器，跳过发送
        if (!serverEndpoint.startsWith('http')) {
            console.log('未配置反馈服务器，仅本地保存');
            return;
        }
        
        fetch(serverEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(feedback)
        })
        .then(response => {
            if (response.ok) {
                console.log('反馈已发送到服务器');
            } else {
                console.warn('发送反馈到服务器失败:', response.status);
            }
        })
        .catch(error => {
            console.warn('发送反馈到服务器时出错:', error);
        });
    }

    /**
     * 加载现有反馈
     */
    loadExistingFeedback() {
        try {
            const existingFeedback = JSON.parse(localStorage.getItem('ota_user_feedback') || '[]');
            this.feedbackData = existingFeedback;
            
            console.log(`已加载 ${this.feedbackData.length} 条历史反馈`);
            
        } catch (error) {
            console.error('加载历史反馈失败:', error);
            this.feedbackData = [];
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage() {
        const successMessage = document.getElementById('success-message');
        successMessage.classList.remove('hidden');
        
        // 3秒后自动隐藏
        setTimeout(() => {
            successMessage.classList.add('hidden');
        }, 3000);
        
        // 滚动到顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    /**
     * 获取反馈统计
     */
    getFeedbackStats() {
        const stats = {
            total: this.feedbackData.length,
            byType: {},
            averageRatings: {},
            recentFeedback: this.feedbackData.slice(-10)
        };
        
        // 按类型统计
        this.feedbackData.forEach(feedback => {
            stats.byType[feedback.type] = (stats.byType[feedback.type] || 0) + 1;
        });
        
        // 计算平均评分
        const generalFeedback = this.feedbackData.filter(f => f.type === 'general');
        if (generalFeedback.length > 0) {
            const totalOverall = generalFeedback.reduce((sum, f) => sum + (f.data.overall_rating || 0), 0);
            const totalUsability = generalFeedback.reduce((sum, f) => sum + (f.data.usability_rating || 0), 0);
            const totalLearning = generalFeedback.reduce((sum, f) => sum + (f.data.learning_rating || 0), 0);
            
            stats.averageRatings = {
                overall: (totalOverall / generalFeedback.length).toFixed(1),
                usability: (totalUsability / generalFeedback.length).toFixed(1),
                learning: (totalLearning / generalFeedback.length).toFixed(1)
            };
        }
        
        return stats;
    }

    /**
     * 导出反馈数据
     */
    exportFeedbackData() {
        const dataStr = JSON.stringify(this.feedbackData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `feedback_export_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        console.log('反馈数据已导出');
    }

    /**
     * 清空反馈数据
     */
    clearFeedbackData() {
        if (confirm('确定要清空所有反馈数据吗？此操作不可撤销。')) {
            this.feedbackData = [];
            localStorage.removeItem('ota_user_feedback');
            
            if (window.OTA && window.OTA.learningStorageManager) {
                window.OTA.learningStorageManager.removeData('user_feedback');
            }
            
            console.log('反馈数据已清空');
        }
    }
}

/**
 * 切换标签页
 */
function switchTab(tabName) {
    // 隐藏所有标签内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 移除所有标签按钮的激活状态
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    
    // 显示选中的标签内容
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    // 激活选中的标签按钮
    event.target.classList.add('active');
}

// 创建全局实例
const feedbackCollectionSystem = new FeedbackCollectionSystem();

// 导出到全局命名空间
window.OTA = window.OTA || {};
window.OTA.feedbackCollectionSystem = feedbackCollectionSystem;

// 向后兼容
window.feedbackCollectionSystem = feedbackCollectionSystem;
window.switchTab = switchTab;

console.log('反馈收集系统已加载', { version: feedbackCollectionSystem.version });
