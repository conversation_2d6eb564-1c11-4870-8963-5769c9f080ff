<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTA渠道同步测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .test-result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        select { width: 100%; padding: 8px; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>🧪 OTA渠道同步测试</h1>
    
    <div class="test-section">
        <div class="test-title">1. 检查OTA渠道映射模组加载</div>
        <div id="moduleTest" class="test-result">检测中...</div>
    </div>

    <div class="test-section">
        <div class="test-title">2. 通用渠道列表</div>
        <select id="commonChannels">
            <option value="">加载中...</option>
        </select>
        <div id="commonChannelsInfo" class="test-result"></div>
    </div>

    <div class="test-section">
        <div class="test-title">3. 用户特定渠道配置（模拟JR Coach用户）</div>
        <select id="userChannels">
            <option value="">加载中...</option>
        </select>
        <div id="userChannelsInfo" class="test-result"></div>
    </div>

    <!-- 加载项目的核心脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/monitoring-wrapper.js"></script>
    <script src="js/ota-channel-mapping.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/api-service.js"></script>
    <script src="js/gemini-service.js"></script>
    <script src="js/managers/form-manager.js"></script>
    <script src="js/managers/price-manager.js"></script>
    <script src="js/managers/event-manager.js"></script>
    <script src="js/managers/state-manager.js"></script>
    <script src="js/managers/realtime-analysis-manager.js"></script>
    <script src="js/ui-manager.js"></script>
    <script src="js/multi-order-manager.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 开始OTA渠道同步测试');

            // 测试1: 检查模组加载
            const moduleTestDiv = document.getElementById('moduleTest');
            if (window.OTA && window.OTA.otaChannelMapping) {
                moduleTestDiv.innerHTML = '✅ OTA渠道映射模组已加载';
                moduleTestDiv.style.background = '#d4edda';
                moduleTestDiv.style.color = '#155724';
            } else {
                moduleTestDiv.innerHTML = '❌ OTA渠道映射模组未加载';
                moduleTestDiv.style.background = '#f8d7da';
                moduleTestDiv.style.color = '#721c24';
            }

            // 测试2: 通用渠道列表
            if (window.OTA && window.OTA.otaChannelMapping && window.OTA.otaChannelMapping.commonChannels) {
                const commonChannels = window.OTA.otaChannelMapping.commonChannels;
                const commonSelect = document.getElementById('commonChannels');
                const commonInfo = document.getElementById('commonChannelsInfo');
                
                commonSelect.innerHTML = '<option value="">选择渠道...</option>' + 
                    commonChannels.map(ch => `<option value="${ch.value}">${ch.text}</option>`).join('');
                
                commonInfo.innerHTML = `✅ 通用渠道列表已加载，共 ${commonChannels.length} 个渠道`;
                commonInfo.style.background = '#d4edda';
                commonInfo.style.color = '#155724';
            } else {
                document.getElementById('commonChannelsInfo').innerHTML = '❌ 通用渠道列表加载失败';
            }

            // 测试3: 用户特定配置
            if (window.OTA && window.OTA.otaChannelMapping && window.OTA.otaChannelMapping.getConfig) {
                // 模拟JR Coach用户（ID: 2666）
                const userConfig = window.OTA.otaChannelMapping.getConfig(2666);
                const userSelect = document.getElementById('userChannels');
                const userInfo = document.getElementById('userChannelsInfo');
                
                if (userConfig && userConfig.options) {
                    userSelect.innerHTML = '<option value="">选择渠道...</option>' + 
                        userConfig.options.map(ch => `<option value="${ch.value}">${ch.text}</option>`).join('');
                    
                    userInfo.innerHTML = `✅ JR Coach用户配置已加载，默认渠道: ${userConfig.default}，可选渠道: ${userConfig.options.length} 个`;
                    userInfo.style.background = '#d4edda';
                    userInfo.style.color = '#155724';
                } else {
                    userInfo.innerHTML = '❌ JR Coach用户配置不存在';
                    userInfo.style.background = '#fff3cd';
                    userInfo.style.color = '#856404';
                }
            }

            // 测试4: 多订单管理器的渠道选项
            setTimeout(() => {
                if (window.OTA && window.OTA.multiOrderManager) {
                    console.log('🧪 测试多订单管理器的OTA渠道选项');
                    try {
                        const manager = window.OTA.multiOrderManager;
                        const options = manager.getOtaChannelOptions();
                        console.log('🧪 获取到的OTA渠道选项:', options);
                        
                        // 添加测试结果到页面
                        const testDiv = document.createElement('div');
                        testDiv.className = 'test-section';
                        testDiv.innerHTML = `
                            <div class="test-title">4. 多订单管理器渠道选项测试</div>
                            <div class="test-result" style="background: #d4edda; color: #155724;">
                                ✅ 多订单管理器已成功集成OTA渠道映射模组<br>
                                生成的选项数量: ${(options.match(/<option/g) || []).length} 个
                            </div>
                        `;
                        document.body.appendChild(testDiv);
                    } catch (error) {
                        console.error('🧪 多订单管理器测试失败:', error);
                    }
                } else {
                    console.log('🧪 多订单管理器未加载');
                }
            }, 1000);
        });
    </script>
</body>
</html>