<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录到创建订单完整流程测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result { 
            margin: 8px 0; 
            padding: 8px 12px; 
            border-radius: 4px; 
            font-size: 14px;
        }
        .success { background-color: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background-color: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background-color: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .warning { background-color: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        .step { background-color: #e7f3ff; color: #004085; border-left: 4px solid #007bff; }
        
        .login-form, .order-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { 
            background: #6c757d; 
            cursor: not-allowed; 
        }
        .data-display {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }
        .status-info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>登录到创建订单完整流程测试</h1>
        <p>此测试验证从登录画面到创建订单画面的完整数据链和运行流程</p>
        
        <div class="test-section">
            <h2>🔧 系统初始化检查</h2>
            <div id="initResults"></div>
            <button onclick="runInitializationTests()" id="initBtn">开始系统初始化检查</button>
        </div>

        <div class="test-section">
            <h2>🔐 模拟登录流程</h2>
            <div class="login-form">
                <div class="form-group">
                    <label>邮箱:</label>
                    <input type="email" id="email" value="<EMAIL>" placeholder="输入邮箱">
                </div>
                <div class="form-group">
                    <label>密码:</label>
                    <input type="password" id="password" value="testpassword" placeholder="输入密码">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="rememberMe"> 记住我
                    </label>
                </div>
                <button onclick="simulateLogin()" id="loginBtn">模拟登录</button>
            </div>
            <div id="loginResults"></div>
        </div>

        <div class="test-section">
            <h2>📊 用户状态和数据验证</h2>
            <div id="userStateResults"></div>
            <button onclick="checkUserState()" id="stateBtn" disabled>检查用户状态</button>
        </div>

        <div class="test-section">
            <h2>📝 创建订单流程测试</h2>
            <div class="order-form">
                <div class="form-group">
                    <label>客户姓名:</label>
                    <input type="text" id="customerName" value="张三" placeholder="输入客户姓名">
                </div>
                <div class="form-group">
                    <label>客户电话:</label>
                    <input type="tel" id="customerPhone" value="13800138000" placeholder="输入客户电话">
                </div>
                <div class="form-group">
                    <label>接送日期:</label>
                    <input type="date" id="pickupDate" value="2024-12-31">
                </div>
                <div class="form-group">
                    <label>接送时间:</label>
                    <input type="time" id="pickupTime" value="10:00">
                </div>
                <div class="form-group">
                    <label>接送地点:</label>
                    <input type="text" id="pickup" value="北京首都国际机场T3航站楼" placeholder="输入接送地点">
                </div>
                <div class="form-group">
                    <label>目的地:</label>
                    <input type="text" id="destination" value="北京王府井大街1号" placeholder="输入目的地">
                </div>
                <div class="form-group">
                    <label>乘客人数:</label>
                    <input type="number" id="pax" value="2" min="1" max="8">
                </div>
                <div class="form-group">
                    <label>行李数量:</label>
                    <input type="number" id="luggage" value="2" min="0">
                </div>
                <div class="form-group">
                    <label>特殊要求:</label>
                    <textarea id="specialRequest" placeholder="输入特殊要求" rows="3">需要儿童安全座椅</textarea>
                </div>
                <button onclick="simulateOrderCreation()" id="orderBtn" disabled>模拟创建订单</button>
            </div>
            <div id="orderResults"></div>
        </div>

        <div class="test-section">
            <h2>📈 完整数据链验证</h2>
            <div id="dataChainResults"></div>
            <button onclick="validateDataChain()" id="chainBtn" disabled>验证完整数据链</button>
        </div>

        <div class="test-section">
            <h2>🎯 测试总结</h2>
            <div id="summaryResults"></div>
        </div>
    </div>

    <!-- 加载核心依赖 - 按照实际项目的加载顺序 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/services/logger.js"></script>
    <script src="js/utils/utils.js"></script>
    <script src="js/bootstrap/app-state.js"></script>
    <script src="js/services/api-service.js"></script>
    <script src="js/services/i18n.js"></script>
    <script src="js/managers/form-manager.js"></script>
    <script src="js/managers/ui-manager.js"></script>
    <script src="js/managers/event-manager.js"></script>

    <script>
        // 测试状态跟踪
        let testState = {
            initialized: false,
            loggedIn: false,
            userDataLoaded: false,
            orderCreated: false,
            errors: [],
            warnings: []
        };

        function addResult(containerId, message, type = 'info', includeTimestamp = true) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            
            const statusIcon = type === 'success' ? '✅' : 
                             type === 'error' ? '❌' : 
                             type === 'warning' ? '⚠️' : 
                             type === 'step' ? '👉' : 'ℹ️';
            
            const timestamp = includeTimestamp ? `[${new Date().toLocaleTimeString()}] ` : '';
            div.innerHTML = `${statusIcon} ${timestamp}${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        // 1. 系统初始化检查
        async function runInitializationTests() {
            document.getElementById('initBtn').disabled = true;
            document.getElementById('initResults').innerHTML = '';
            
            addResult('initResults', '开始系统初始化检查...', 'step');
            
            try {
                // 检查核心服务是否加载
                const coreServices = [
                    { name: 'window.OTA', getter: () => window.OTA },
                    { name: 'window.getLogger', getter: () => window.getLogger },
                    { name: 'window.getAppState', getter: () => window.getAppState },
                    { name: 'window.getAPIService', getter: () => window.getAPIService }
                ];

                let initSuccess = true;
                for (const service of coreServices) {
                    try {
                        const result = service.getter();
                        if (result) {
                            addResult('initResults', `${service.name} 已加载`, 'success');
                        } else {
                            addResult('initResults', `${service.name} 未找到`, 'error');
                            initSuccess = false;
                        }
                    } catch (error) {
                        addResult('initResults', `${service.name} 检查失败: ${error.message}`, 'error');
                        initSuccess = false;
                    }
                }

                // 检查服务实例
                if (initSuccess) {
                    try {
                        const logger = window.getLogger();
                        const appState = window.getAppState();
                        const apiService = window.getAPIService();

                        if (logger && typeof logger.log === 'function') {
                            addResult('initResults', 'Logger 服务正常', 'success');
                        } else {
                            addResult('initResults', 'Logger 服务异常', 'error');
                            initSuccess = false;
                        }

                        if (appState && typeof appState.get === 'function') {
                            addResult('initResults', 'AppState 服务正常', 'success');
                        } else {
                            addResult('initResults', 'AppState 服务异常', 'error');
                            initSuccess = false;
                        }

                        if (apiService && typeof apiService.login === 'function') {
                            addResult('initResults', 'API 服务正常', 'success');
                        } else {
                            addResult('initResults', 'API 服务异常', 'error');
                            initSuccess = false;
                        }
                    } catch (error) {
                        addResult('initResults', `服务实例检查失败: ${error.message}`, 'error');
                        initSuccess = false;
                    }
                }

                testState.initialized = initSuccess;
                
                if (initSuccess) {
                    addResult('initResults', '🎉 系统初始化检查通过', 'success');
                    document.getElementById('loginBtn').disabled = false;
                } else {
                    addResult('initResults', '❌ 系统初始化检查失败', 'error');
                    testState.errors.push('系统初始化失败');
                }

            } catch (error) {
                addResult('initResults', `初始化检查异常: ${error.message}`, 'error');
                testState.errors.push(`初始化异常: ${error.message}`);
            }
        }

        // 2. 模拟登录流程
        async function simulateLogin() {
            document.getElementById('loginBtn').disabled = true;
            document.getElementById('loginResults').innerHTML = '';
            
            addResult('loginResults', '开始模拟登录流程...', 'step');
            
            try {
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const rememberMe = document.getElementById('rememberMe').checked;

                addResult('loginResults', `尝试登录: ${email}`, 'info');
                
                // 获取必要的服务
                const logger = window.getLogger();
                const appState = window.getAppState();
                const apiService = window.getAPIService();

                // 检查登录前状态
                const preLoginState = appState.get('auth.isLoggedIn');
                addResult('loginResults', `登录前状态: ${preLoginState ? '已登录' : '未登录'}`, 'info');

                // 模拟API调用 - 这里我们模拟成功的响应
                addResult('loginResults', '发送登录请求到API...', 'info');
                
                // 由于我们无法进行真实的API调用，我们模拟一个成功的响应
                const mockApiResponse = {
                    status: true,
                    token: '2409|F0hye4nKqL58qhcdW8knwLwLMbQltjl35uggBwGA',
                    user: {
                        id: 1,
                        name: '测试用户',
                        email: email,
                        role: 'user'
                    }
                };

                // 模拟登录成功的处理逻辑
                addResult('loginResults', '收到API响应，处理登录数据...', 'info');
                
                // 提取token
                let actualToken = mockApiResponse.token;
                if (mockApiResponse.token.includes('|')) {
                    actualToken = mockApiResponse.token.split('|')[1];
                }

                // 使用appState设置认证信息
                appState.setAuth(actualToken, mockApiResponse.user, rememberMe);
                
                // 验证登录状态
                const postLoginState = appState.get('auth.isLoggedIn');
                const userInfo = appState.get('auth.user');
                
                if (postLoginState && userInfo) {
                    addResult('loginResults', '✅ 登录成功', 'success');
                    addResult('loginResults', `用户信息: ${userInfo.name} (${userInfo.email})`, 'success');
                    addResult('loginResults', `Token已保存: ${actualToken.substring(0, 20)}...`, 'success');
                    
                    testState.loggedIn = true;
                    document.getElementById('stateBtn').disabled = false;
                    
                    // 记录用户操作
                    logger.logUserAction('登录成功', { 
                        email: email, 
                        rememberMe: rememberMe,
                        timestamp: new Date().toISOString()
                    });
                    
                } else {
                    addResult('loginResults', '❌ 登录状态设置失败', 'error');
                    testState.errors.push('登录状态设置失败');
                }

            } catch (error) {
                addResult('loginResults', `登录流程异常: ${error.message}`, 'error');
                addResult('loginResults', `错误堆栈: ${error.stack}`, 'error');
                testState.errors.push(`登录异常: ${error.message}`);
            }
        }

        // 3. 检查用户状态
        async function checkUserState() {
            document.getElementById('stateBtn').disabled = true;
            document.getElementById('userStateResults').innerHTML = '';
            
            addResult('userStateResults', '检查用户状态和数据...', 'step');
            
            try {
                const appState = window.getAppState();
                const logger = window.getLogger();

                // 检查认证状态
                const isLoggedIn = appState.get('auth.isLoggedIn');
                const user = appState.get('auth.user');
                const token = appState.get('auth.token');
                const tokenExpiry = appState.get('auth.tokenExpiry');

                addResult('userStateResults', `登录状态: ${isLoggedIn ? '已登录' : '未登录'}`, 
                    isLoggedIn ? 'success' : 'error');

                if (user) {
                    addResult('userStateResults', `用户信息: ID=${user.id}, 姓名=${user.name}, 邮箱=${user.email}`, 'success');
                    addResult('userStateResults', `用户角色: ${user.role}`, 'info');
                } else {
                    addResult('userStateResults', '用户信息未找到', 'error');
                }

                if (token) {
                    addResult('userStateResults', `Token: ${token.substring(0, 20)}... (长度: ${token.length})`, 'success');
                } else {
                    addResult('userStateResults', 'Token未找到', 'error');
                }

                if (tokenExpiry) {
                    const expiryDate = new Date(tokenExpiry);
                    const now = new Date();
                    const isExpired = expiryDate < now;
                    
                    addResult('userStateResults', 
                        `Token过期时间: ${expiryDate.toLocaleString()} ${isExpired ? '(已过期)' : '(未过期)'}`, 
                        isExpired ? 'warning' : 'success');
                } else {
                    addResult('userStateResults', 'Token过期时间未设置', 'warning');
                }

                // 检查系统数据
                const systemData = appState.get('systemData');
                if (systemData) {
                    addResult('userStateResults', '系统数据已加载', 'success');
                    const lastUpdated = appState.get('systemData.lastUpdated');
                    if (lastUpdated) {
                        addResult('userStateResults', `系统数据最后更新: ${new Date(lastUpdated).toLocaleString()}`, 'info');
                    }
                } else {
                    addResult('userStateResults', '系统数据未加载 (这是正常的)', 'info');
                }

                // 检查localStorage持久化
                const storedAuth = localStorage.getItem('ota_auth');
                if (storedAuth) {
                    try {
                        const authData = JSON.parse(storedAuth);
                        addResult('userStateResults', '本地存储的认证数据存在', 'success');
                        addResult('userStateResults', `存储的用户: ${authData.user?.name || '未知'}`, 'info');
                    } catch (e) {
                        addResult('userStateResults', '本地存储的认证数据格式异常', 'warning');
                    }
                } else {
                    addResult('userStateResults', '本地存储的认证数据不存在', 'warning');
                }

                testState.userDataLoaded = true;
                document.getElementById('orderBtn').disabled = false;
                
                addResult('userStateResults', '✅ 用户状态检查完成', 'success');

            } catch (error) {
                addResult('userStateResults', `用户状态检查异常: ${error.message}`, 'error');
                testState.errors.push(`用户状态检查异常: ${error.message}`);
            }
        }

        // 4. 模拟创建订单
        async function simulateOrderCreation() {
            document.getElementById('orderBtn').disabled = true;
            document.getElementById('orderResults').innerHTML = '';
            
            addResult('orderResults', '开始模拟创建订单流程...', 'step');
            
            try {
                const appState = window.getAppState();
                const apiService = window.getAPIService();
                const logger = window.getLogger();

                // 检查登录状态
                const isLoggedIn = appState.get('auth.isLoggedIn');
                if (!isLoggedIn) {
                    addResult('orderResults', '❌ 用户未登录，无法创建订单', 'error');
                    return;
                }

                // 收集表单数据
                const orderData = {
                    customer_name: document.getElementById('customerName').value,
                    customer_phone: document.getElementById('customerPhone').value,
                    date: document.getElementById('pickupDate').value,
                    time: document.getElementById('pickupTime').value,
                    pickup: document.getElementById('pickup').value,
                    destination: document.getElementById('destination').value,
                    pax: parseInt(document.getElementById('pax').value),
                    luggage_number: parseInt(document.getElementById('luggage').value),
                    special_request: document.getElementById('specialRequest').value,
                    created_by: appState.get('auth.user.name'),
                    created_at: new Date().toISOString()
                };

                addResult('orderResults', '表单数据收集完成', 'success');
                addResult('orderResults', `订单数据: ${JSON.stringify(orderData, null, 2)}`, 'info');

                // 验证必填字段
                const requiredFields = ['customer_name', 'customer_phone', 'date', 'time', 'pickup', 'destination'];
                let validationPassed = true;
                
                for (const field of requiredFields) {
                    if (!orderData[field] || orderData[field].toString().trim() === '') {
                        addResult('orderResults', `❌ 必填字段缺失: ${field}`, 'error');
                        validationPassed = false;
                    }
                }

                if (!validationPassed) {
                    addResult('orderResults', '❌ 表单验证失败，无法创建订单', 'error');
                    testState.errors.push('表单验证失败');
                    return;
                }

                addResult('orderResults', '✅ 表单验证通过', 'success');

                // 模拟API调用创建订单
                addResult('orderResults', '发送创建订单请求...', 'info');
                
                // 模拟成功的API响应
                const mockOrderResponse = {
                    success: true,
                    order_id: Math.floor(Math.random() * 10000) + 1000,
                    status: 'pending',
                    message: '订单创建成功',
                    data: {
                        ...orderData,
                        order_id: Math.floor(Math.random() * 10000) + 1000,
                        status: 'pending'
                    }
                };

                // 处理订单创建响应
                if (mockOrderResponse.success) {
                    addResult('orderResults', `✅ 订单创建成功, ID: ${mockOrderResponse.order_id}`, 'success');
                    
                    // 更新应用状态
                    appState.setCurrentOrder(mockOrderResponse.data);
                    
                    // 记录用户操作
                    logger.logUserAction('创建订单', {
                        order_id: mockOrderResponse.order_id,
                        customer: orderData.customer_name,
                        route: `${orderData.pickup} → ${orderData.destination}`,
                        timestamp: new Date().toISOString()
                    });
                    
                    // 检查订单是否正确保存到状态中
                    const currentOrder = appState.get('currentOrder');
                    if (currentOrder && currentOrder.order_id === mockOrderResponse.order_id) {
                        addResult('orderResults', '✅ 订单状态已更新', 'success');
                        testState.orderCreated = true;
                        document.getElementById('chainBtn').disabled = false;
                    } else {
                        addResult('orderResults', '⚠️ 订单状态更新异常', 'warning');
                        testState.warnings.push('订单状态更新异常');
                    }
                    
                } else {
                    addResult('orderResults', `❌ 订单创建失败: ${mockOrderResponse.message}`, 'error');
                    testState.errors.push(`订单创建失败: ${mockOrderResponse.message}`);
                }

            } catch (error) {
                addResult('orderResults', `创建订单异常: ${error.message}`, 'error');
                addResult('orderResults', `错误堆栈: ${error.stack}`, 'error');
                testState.errors.push(`创建订单异常: ${error.message}`);
            }
        }

        // 5. 验证完整数据链
        async function validateDataChain() {
            document.getElementById('chainBtn').disabled = true;
            document.getElementById('dataChainResults').innerHTML = '';
            
            addResult('dataChainResults', '验证完整数据链...', 'step');
            
            try {
                const appState = window.getAppState();
                const logger = window.getLogger();

                // 验证数据链的每个环节
                addResult('dataChainResults', '=== 数据链验证 ===', 'info');

                // 1. 认证数据链
                const authUser = appState.get('auth.user');
                const authToken = appState.get('auth.token');
                const authStatus = appState.get('auth.isLoggedIn');
                
                if (authUser && authToken && authStatus) {
                    addResult('dataChainResults', '✅ 认证数据链完整', 'success');
                    addResult('dataChainResults', `认证用户: ${authUser.name} (${authUser.email})`, 'info');
                } else {
                    addResult('dataChainResults', '❌ 认证数据链不完整', 'error');
                }

                // 2. 订单数据链
                const currentOrder = appState.get('currentOrder');
                if (currentOrder) {
                    addResult('dataChainResults', '✅ 订单数据链存在', 'success');
                    addResult('dataChainResults', `当前订单: ID=${currentOrder.order_id}, 客户=${currentOrder.customer_name}`, 'info');
                } else {
                    addResult('dataChainResults', '❌ 订单数据链缺失', 'error');
                }

                // 3. 日志数据链
                const logHistory = logger.getRecentLogs ? logger.getRecentLogs(5) : [];
                if (logHistory && logHistory.length > 0) {
                    addResult('dataChainResults', `✅ 日志数据链存在 (${logHistory.length} 条记录)`, 'success');
                    logHistory.forEach((log, index) => {
                        addResult('dataChainResults', `  ${index + 1}. [${log.level}] ${log.message}`, 'info');
                    });
                } else {
                    addResult('dataChainResults', '⚠️ 日志数据链为空或不可访问', 'warning');
                }

                // 4. localStorage持久化验证
                const persistentData = {
                    auth: localStorage.getItem('ota_auth'),
                    order: localStorage.getItem('ota_current_order'),
                    settings: localStorage.getItem('ota_settings')
                };

                let persistentCount = 0;
                Object.keys(persistentData).forEach(key => {
                    if (persistentData[key]) {
                        persistentCount++;
                        addResult('dataChainResults', `✅ 持久化数据存在: ${key}`, 'success');
                    } else {
                        addResult('dataChainResults', `⚠️ 持久化数据缺失: ${key}`, 'warning');
                    }
                });

                // 5. 数据一致性验证
                addResult('dataChainResults', '=== 数据一致性验证 ===', 'info');
                
                if (persistentData.auth) {
                    try {
                        const storedAuth = JSON.parse(persistentData.auth);
                        const currentAuth = {
                            user: authUser,
                            token: authToken,
                            isLoggedIn: authStatus
                        };
                        
                        const isConsistent = storedAuth.user?.email === currentAuth.user?.email &&
                                           storedAuth.token === currentAuth.token;
                        
                        if (isConsistent) {
                            addResult('dataChainResults', '✅ 认证数据一致性验证通过', 'success');
                        } else {
                            addResult('dataChainResults', '⚠️ 认证数据一致性存在差异', 'warning');
                        }
                    } catch (e) {
                        addResult('dataChainResults', '❌ 认证数据一致性验证失败', 'error');
                    }
                }

                // 6. 完整流程状态检查
                addResult('dataChainResults', '=== 完整流程状态 ===', 'info');
                
                const flowStatus = {
                    '系统初始化': testState.initialized,
                    '用户登录': testState.loggedIn,
                    '用户数据加载': testState.userDataLoaded,
                    '订单创建': testState.orderCreated
                };

                let allFlowsComplete = true;
                Object.keys(flowStatus).forEach(step => {
                    const status = flowStatus[step];
                    addResult('dataChainResults', 
                        `${step}: ${status ? '完成' : '未完成'}`, 
                        status ? 'success' : 'error');
                    if (!status) allFlowsComplete = false;
                });

                if (allFlowsComplete) {
                    addResult('dataChainResults', '🎉 完整数据链验证通过', 'success');
                } else {
                    addResult('dataChainResults', '❌ 完整数据链验证未通过', 'error');
                }

            } catch (error) {
                addResult('dataChainResults', `数据链验证异常: ${error.message}`, 'error');
                testState.errors.push(`数据链验证异常: ${error.message}`);
            }

            // 生成最终总结
            generateSummary();
        }

        // 6. 生成测试总结
        function generateSummary() {
            const summaryContainer = document.getElementById('summaryResults');
            summaryContainer.innerHTML = '';
            
            addResult('summaryResults', '=== 测试总结报告 ===', 'step');
            
            // 测试完成状态
            const completedTests = [
                testState.initialized,
                testState.loggedIn,
                testState.userDataLoaded,
                testState.orderCreated
            ].filter(Boolean).length;
            
            addResult('summaryResults', `完成测试: ${completedTests}/4`, 'info');
            
            // 错误和警告统计
            if (testState.errors.length > 0) {
                addResult('summaryResults', `发现错误: ${testState.errors.length} 个`, 'error');
                testState.errors.forEach((error, index) => {
                    addResult('summaryResults', `  ${index + 1}. ${error}`, 'error');
                });
            }
            
            if (testState.warnings.length > 0) {
                addResult('summaryResults', `发现警告: ${testState.warnings.length} 个`, 'warning');
                testState.warnings.forEach((warning, index) => {
                    addResult('summaryResults', `  ${index + 1}. ${warning}`, 'warning');
                });
            }
            
            // 最终结论
            if (completedTests === 4 && testState.errors.length === 0) {
                addResult('summaryResults', '🎉 登录到创建订单完整流程测试通过！', 'success');
                addResult('summaryResults', '所有数据链和运行流程都正常工作', 'success');
            } else if (completedTests >= 2 && testState.errors.length <= 2) {
                addResult('summaryResults', '✅ 基本功能正常，存在轻微问题', 'warning');
                addResult('summaryResults', '主要流程可以工作，建议修复发现的问题', 'warning');
            } else {
                addResult('summaryResults', '❌ 存在严重问题，需要修复', 'error');
                addResult('summaryResults', '请检查错误信息并进行相应修复', 'error');
            }
        }

        // 页面加载完成后自动开始初始化检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('initResults', '页面加载完成，等待开始测试...', 'info');
            }, 500);
        });
    </script>
</body>
</html>