/**
 * 开发时检查脚本
 * 用于在开发过程中自动检测接口一致性问题
 * <AUTHOR> System
 * @created 2025-01-29
 */

(function() {
    'use strict';

    /**
     * 开发时检查器
     */
    class DevChecker {
        constructor() {
            this.checks = [];
            this.issues = [];
            this.isEnabled = true;
        }

        /**
         * 添加检查项
         * @param {string} name - 检查项名称
         * @param {Function} checkFn - 检查函数
         */
        addCheck(name, checkFn) {
            this.checks.push({ name, checkFn });
        }

        /**
         * 运行所有检查
         */
        async runChecks() {
            if (!this.isEnabled) return;

            console.log('🔍 开始开发时检查...');
            this.issues = [];

            for (const { name, checkFn } of this.checks) {
                try {
                    const result = await checkFn();
                    if (result && result.issues) {
                        this.issues.push(...result.issues.map(issue => ({ check: name, ...issue })));
                    }
                } catch (error) {
                    this.issues.push({
                        check: name,
                        type: 'error',
                        message: `检查执行失败: ${error.message}`,
                        severity: 'high'
                    });
                }
            }

            this.reportIssues();
        }

        /**
         * 报告发现的问题
         */
        reportIssues() {
            if (this.issues.length === 0) {
                console.log('✅ 开发时检查通过，未发现问题');
                return;
            }

            console.warn(`⚠️ 发现 ${this.issues.length} 个问题:`);
            
            this.issues.forEach((issue, index) => {
                const icon = issue.severity === 'high' ? '🚨' : issue.severity === 'medium' ? '⚠️' : 'ℹ️';
                console.warn(`${icon} [${issue.check}] ${issue.message}`);
                
                if (issue.suggestion) {
                    console.log(`   💡 建议: ${issue.suggestion}`);
                }
            });
        }

        /**
         * 启用/禁用检查
         * @param {boolean} enabled - 是否启用
         */
        setEnabled(enabled) {
            this.isEnabled = enabled;
            console.log(`开发时检查已${enabled ? '启用' : '禁用'}`);
        }
    }

    // 创建检查器实例
    const devChecker = new DevChecker();

    // 检查项：依赖容器接口完整性
    devChecker.addCheck('依赖容器接口完整性', () => {
        const issues = [];
        const container = window.OTA?.container;

        if (!container) {
            issues.push({
                type: 'error',
                message: 'window.OTA.container 不存在',
                severity: 'high',
                suggestion: '确保依赖容器已正确初始化'
            });
            return { issues };
        }

        // 检查必需方法
        const requiredMethods = [
            { name: 'isInitialized', description: '检查初始化状态' },
            { name: 'getStatus', description: '获取状态信息' },
            { name: 'register', description: '注册服务' },
            { name: 'resolve', description: '解析服务' }
        ];

        requiredMethods.forEach(({ name, description }) => {
            if (typeof container[name] !== 'function') {
                issues.push({
                    type: 'error',
                    message: `缺少必需方法: ${name}`,
                    severity: 'high',
                    suggestion: `在 DependencyContainer 类中添加 ${name} 方法 (${description})`
                });
            }
        });

        // 检查必需属性
        if (!('initialized' in container)) {
            issues.push({
                type: 'error',
                message: '缺少 initialized 属性',
                severity: 'high',
                suggestion: '在 DependencyContainer 构造函数中初始化 this.initialized = false'
            });
        }

        // 检查方法与属性的一致性
        if (typeof container.isInitialized === 'function' && 'initialized' in container) {
            try {
                const methodResult = container.isInitialized();
                const propertyValue = container.initialized;
                if (methodResult !== propertyValue) {
                    issues.push({
                        type: 'warning',
                        message: 'isInitialized() 方法与 initialized 属性不一致',
                        severity: 'medium',
                        suggestion: '确保 isInitialized() 方法返回 this.initialized 的值'
                    });
                }
            } catch (error) {
                issues.push({
                    type: 'error',
                    message: `isInitialized() 方法调用失败: ${error.message}`,
                    severity: 'high',
                    suggestion: '检查 isInitialized() 方法的实现'
                });
            }
        }

        return { issues };
    });

    // 检查项：Registry 接口完整性
    devChecker.addCheck('Registry 接口完整性', () => {
        const issues = [];
        const Registry = window.OTA?.Registry;

        if (!Registry) {
            issues.push({
                type: 'error',
                message: 'window.OTA.Registry 不存在',
                severity: 'high',
                suggestion: '确保 Registry 模块已正确加载'
            });
            return { issues };
        }

        // 检查必需方法
        const requiredMethods = [
            { name: 'getRegistryInfo', description: '获取注册信息' },
            { name: 'register', description: '注册项目' },
            { name: 'unregister', description: '取消注册' }
        ];

        requiredMethods.forEach(({ name, description }) => {
            if (typeof Registry[name] !== 'function') {
                issues.push({
                    type: 'error',
                    message: `Registry 缺少必需方法: ${name}`,
                    severity: 'high',
                    suggestion: `在 Registry 中添加 ${name} 方法 (${description})`
                });
            }
        });

        // 测试 getRegistryInfo 方法
        if (typeof Registry.getRegistryInfo === 'function') {
            try {
                const registryInfo = Registry.getRegistryInfo();
                if (!registryInfo || typeof registryInfo !== 'object') {
                    issues.push({
                        type: 'warning',
                        message: 'getRegistryInfo() 返回值格式不正确',
                        severity: 'medium',
                        suggestion: 'getRegistryInfo() 应该返回包含注册信息的对象'
                    });
                }
            } catch (error) {
                issues.push({
                    type: 'error',
                    message: `getRegistryInfo() 方法调用失败: ${error.message}`,
                    severity: 'high',
                    suggestion: '检查 getRegistryInfo() 方法的实现和错误处理'
                });
            }
        }

        return { issues };
    });

    // 检查项：全局命名空间结构
    devChecker.addCheck('全局命名空间结构', () => {
        const issues = [];

        // 检查基础命名空间
        if (!window.OTA) {
            issues.push({
                type: 'error',
                message: 'window.OTA 命名空间不存在',
                severity: 'high',
                suggestion: '确保在所有模块加载前初始化 window.OTA = window.OTA || {}'
            });
            return { issues };
        }

        // 检查核心模块
        const coreModules = [
            { name: 'container', description: '依赖容器' },
            { name: 'Registry', description: '注册中心' }
        ];

        coreModules.forEach(({ name, description }) => {
            if (!window.OTA[name]) {
                issues.push({
                    type: 'warning',
                    message: `核心模块 ${name} 不存在`,
                    severity: 'medium',
                    suggestion: `确保 ${description} 模块已正确加载到 window.OTA.${name}`
                });
            }
        });

        return { issues };
    });

    // 自动运行检查
    function autoCheck() {
        // 等待系统初始化完成
        setTimeout(() => {
            devChecker.runChecks();
        }, 2000);
    }

    // 导出到全局命名空间
    window.OTA = window.OTA || {};
    window.OTA.devChecker = devChecker;

    // 在开发环境中自动运行检查
    if (window.location.hostname === 'localhost' || window.location.protocol === 'file:') {
        autoCheck();
    }

    console.log('✅ 开发时检查脚本已加载');

})();
