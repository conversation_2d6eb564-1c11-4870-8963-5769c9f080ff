/**
 * @OTA_SERVICE Gemini AI服务模块 - 轻量级入口文件
 * 🏷️ 标签: @GEMINI_SERVICE_ENTRY
 * 📝 说明: 作为向后兼容的入口点，将所有处理委托给新的模块化架构
 * 🎯 功能: 向后兼容接口、协调器委托、配置管理
 * <AUTHOR>
 * @version 2.0.0 (重构版本)
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * Gemini AI服务类 - 轻量级入口点
     * 保持与原有接口的完全兼容性，将实际处理委托给新的模块化架构
     */
    class GeminiService {
        constructor() {
            this.logger = window.getLogger?.() || console;
            
            // 基本配置（详细配置由coordinator管理）
            this.version = '2.0.0';

            // 获取协调器实例（延迟初始化）
            this.coordinator = null;
            
            // 简化配置结构（详细配置由coordinator管理）
            this.realtimeConfig = { enabled: true };
            this.idMappings = {};
            this.isInitialized = false;
            
            // 初始化服务
            this.initialize();
        }

        /**
         * 初始化服务
         */
        async initialize() {
            if (this.isInitialized) {
                return;
            }

            try {
                this.logger.log('Gemini服务初始化开始', 'info');
                
                // 获取协调器实例
                this.coordinator = window.OTA?.gemini?.getGeminiCoordinator?.();
                
                if (!this.coordinator) {
                    this.logger.log('Gemini协调器未找到，将使用降级模式', 'warning');
                }

                this.isInitialized = true;
                this.logger.log('Gemini服务初始化完成', 'info');

            } catch (error) {
                this.logger.logError('Gemini服务初始化失败', error);
                throw error;
            }
        }

        /**
         * 获取协调器实例（确保已初始化）
         * @returns {Object|null} 协调器实例
         */
        getCoordinator() {
            if (!this.coordinator) {
                this.coordinator = window.OTA?.gemini?.getGeminiCoordinator?.();
            }
            return this.coordinator;
        }

        /**
         * 解析订单文本（向后兼容接口）
         * @param {string} orderText - 订单文本
         * @param {boolean} isRealtime - 是否实时分析
         * @returns {Promise<Object>} 解析结果
         */
        async parseOrder(orderText, isRealtime = false) {
            try {
                const coordinator = this.getCoordinator();
                if (coordinator) {
                    return await coordinator.parseOrderCompatible(orderText, isRealtime);
                }
                
                // 降级处理
                return this.fallbackParseOrder(orderText, isRealtime);
                
            } catch (error) {
                this.logger.logError('parseOrder调用失败', error);
                return { success: false, error: error.message };
            }
        }

        /**
         * 解析多个订单（向后兼容接口）
         * @param {Array} orderSegments - 订单片段数组
         * @returns {Promise<Object>} 解析结果
         */
        async parseMultipleOrders(orderSegments) {
            try {
                const coordinator = this.getCoordinator();
                if (coordinator) {
                    return await coordinator.parseMultipleOrdersCompatible(orderSegments);
                }
                
                // 降级处理
                return this.fallbackParseMultipleOrders(orderSegments);
                
            } catch (error) {
                this.logger.logError('parseMultipleOrders调用失败', error);
                return { success: false, error: error.message };
            }
        }

        /**
         * 分析图片（向后兼容接口）
         * @param {string} base64Image - Base64图片数据
         * @param {Object} options - 分析选项
         * @returns {Promise<Object>} 分析结果
         */
        async analyzeImage(base64Image, options = {}) {
            try {
                const coordinator = this.getCoordinator();
                if (coordinator) {
                    return await coordinator.analyzeImageCompatible(base64Image, options);
                }
                
                // 降级处理
                return this.fallbackAnalyzeImage(base64Image, options);
                
            } catch (error) {
                this.logger.logError('analyzeImage调用失败', error);
                return { success: false, error: error.message };
            }
        }

        /**
         * 获取服务状态（向后兼容接口）
         * @returns {Object} 服务状态
         */
        getStatus() {
            try {
                const coordinator = this.getCoordinator();
                if (coordinator) {
                    return coordinator.getStatusCompatible();
                }
                
                // 降级状态
                return {
                    isInitialized: this.isInitialized,
                    coordinatorAvailable: false,
                    mode: 'fallback'
                };
                
            } catch (error) {
                this.logger.logError('getStatus调用失败', error);
                return { error: error.message };
            }
        }

        /**
         * 检查服务是否可用
         * @returns {boolean} 服务是否可用
         */
        isAvailable() {
            try {
                const coordinator = this.getCoordinator();
                return this.isInitialized && coordinator !== null;
            } catch (error) {
                this.logger.logError('isAvailable检查失败', error);
                return false;
            }
        }

        /**
         * 配置实时分析（向后兼容接口）
         * @param {Object} config - 配置对象
         * @returns {Object} 配置结果
         */
        configureRealtimeAnalysis(config) {
            try {
                const coordinator = this.getCoordinator();
                if (coordinator) {
                    return coordinator.configureRealtimeAnalysisCompatible(config);
                }
                
                // 降级处理：更新本地配置
                Object.assign(this.realtimeConfig, config);
                return { success: true, config: this.realtimeConfig };
                
            } catch (error) {
                this.logger.logError('configureRealtimeAnalysis调用失败', error);
                return { success: false, error: error.message };
            }
        }

        /**
         * 更新ID映射（向后兼容接口）
         * @param {Object} systemData - 系统数据
         * @returns {Object} 更新结果
         */
        updateIdMappings(systemData) {
            try {
                const coordinator = this.getCoordinator();
                if (coordinator) {
                    return coordinator.updateIdMappingsCompatible(systemData);
                }
                
                // 降级处理：更新本地映射
                if (systemData.backend_users) {
                    this.idMappings.backendUsers = systemData.backend_users;
                }
                if (systemData.service_types) {
                    this.idMappings.serviceTypes = systemData.service_types;
                }
                if (systemData.car_types) {
                    this.idMappings.carTypes = systemData.car_types;
                }
                if (systemData.languages) {
                    this.idMappings.languages = systemData.languages;
                }
                
                return { success: true, mappings: this.idMappings };
                
            } catch (error) {
                this.logger.logError('updateIdMappings调用失败', error);
                return { success: false, error: error.message };
            }
        }

        /**
         * 降级处理：基本订单解析
         * @param {string} orderText - 订单文本
         * @param {boolean} isRealtime - 是否实时分析
         * @returns {Promise<Object>} 解析结果
         */
        async fallbackParseOrder(orderText, isRealtime = false) {
            this.logger.logWarning('使用降级模式解析订单');
            
            // 基本的文本解析逻辑
            const basicData = {
                customer_name: this.extractBasicField(orderText, ['客户', '姓名', 'name', 'customer']),
                customer_contact: this.extractBasicField(orderText, ['电话', '联系', 'phone', 'contact']),
                pickup: this.extractBasicField(orderText, ['接送', '出发', 'pickup', 'from']),
                dropoff: this.extractBasicField(orderText, ['目的地', '到达', 'destination', 'to']),
                ota_reference_number: this.extractBasicField(orderText, ['订单号', '参考号', 'reference', 'order']),
                raw_text: orderText,
                _fallback_mode: true
            };

            return {
                success: true,
                data: basicData,
                confidence: 0.3,
                source: 'fallback'
            };
        }

        /**
         * 降级处理：多订单解析
         * @param {Array} orderSegments - 订单片段
         * @returns {Promise<Object>} 解析结果
         */
        async fallbackParseMultipleOrders(orderSegments) {
            this.logger.logWarning('使用降级模式解析多订单');
            
            const results = [];
            for (let i = 0; i < orderSegments.length; i++) {
                const result = await this.fallbackParseOrder(orderSegments[i], false);
                if (result.success) {
                    result.data.order_index = i + 1;
                    results.push(result.data);
                }
            }

            return {
                success: true,
                data: results,
                confidence: 0.3,
                source: 'fallback'
            };
        }

        /**
         * 降级处理：图片分析
         * @param {string} base64Image - Base64图片数据
         * @param {Object} options - 分析选项
         * @returns {Promise<Object>} 分析结果
         */
        async fallbackAnalyzeImage(base64Image, options = {}) {
            this.logger.logWarning('使用降级模式分析图片');
            
            // 基本的图片信息提取
            const basicData = {
                image_analyzed: true,
                extracted_text: '图片分析功能暂不可用',
                confidence: 0.1,
                _fallback_mode: true
            };

            return {
                success: true,
                data: basicData,
                confidence: 0.1,
                source: 'fallback'
            };
        }

        /**
         * 基本字段提取（降级处理辅助方法）
         * @param {string} text - 文本内容
         * @param {Array} keywords - 关键词列表
         * @returns {string|null} 提取的字段值
         */
        extractBasicField(text, keywords) {
            for (const keyword of keywords) {
                const regex = new RegExp(`${keyword}[：:：\\s]*([^\\n\\r，,。.；;]+)`, 'i');
                const match = text.match(regex);
                if (match && match[1]) {
                    return match[1].trim();
                }
            }
            return null;
        }
    }

    // 全局注册GeminiService
    window.OTA.GeminiService = GeminiService;
    
    // 创建全局实例
    window.OTA.geminiService = new GeminiService();
    
    // 注意：全局接口定义已移至gemini-coordinator.js以避免重复
    // 这里只保留OTA命名空间内的注册，全局接口由coordinator统一管理

    // 日志记录
    const logger = window.OTA.getService('logger');
    logger.log('Gemini服务轻量级入口文件加载完成', 'info', {
        version: '2.0.0',
        mode: 'lightweight_entry',
        coordinatorRequired: true
    });

})();
