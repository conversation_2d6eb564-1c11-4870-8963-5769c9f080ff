/**
 * 服务定位器 - 统一服务获取接口
 * 解决当前系统中 window.OTA.xxx || window.xxx 的双重模式问题
 * 
 * 使用方式:
 * - 替换所有 getAppState() 为 getService('appState')
 * - 替换所有 getLogger() 为 getService('logger')
 * - 统一所有依赖获取方式
 */

// 确保OTA命名空间和依赖容器存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 服务定位器类
     * 提供统一的服务获取接口，兼容旧的获取方式
     */
    class ServiceLocator {
        constructor() {
            this.container = null;
            this.fallbackMap = new Map();
            this.migrationWarnings = new Set();
            
            // 监控数据收集
            this.serviceUsageStats = new Map();
            this.serviceAccessTimes = new Map();
            this.fallbackUsageCount = new Map();
            this.monitoringEnabled = true;
        }

        /**
         * 初始化服务定位器
         * @param {DependencyContainer} container - 依赖容器实例
         */
        init(container) {
            this.container = container;
            this.setupFallbackMap();
            this.registerCoreServices();
            console.log('✅ 服务定位器已初始化');
        }

        /**
         * 设置降级映射
         * 用于兼容旧的获取方式
         */
        setupFallbackMap() {
            // 核心服务
            this.fallbackMap.set('appState', () => window.OTA.appState || window.appState);
            this.fallbackMap.set('logger', () => window.OTA.logger || window.logger);
            this.fallbackMap.set('apiService', () => window.OTA.apiService || window.apiService);
            this.fallbackMap.set('geminiService', () => window.OTA.geminiService || window.geminiService);
            this.fallbackMap.set('uiManager', () => window.OTA.uiManager || window.uiManager);
            this.fallbackMap.set('utils', () => window.OTA.utils || window.utils);
            this.fallbackMap.set('i18nManager', () => window.OTA.i18nManager || window.i18nManager);
            
            // 功能管理器
            this.fallbackMap.set('formManager', () => window.OTA.formManager || window.formManager);
            this.fallbackMap.set('imageUploadManager', () => window.OTA.imageUploadManager || window.imageUploadManager);
            this.fallbackMap.set('currencyConverter', () => window.OTA.currencyConverter || window.currencyConverter);
            this.fallbackMap.set('multiOrderManager', () => window.OTA.multiOrderManager || window.multiOrderManager);
            this.fallbackMap.set('orderHistoryManager', () => window.OTA.orderHistoryManager || window.orderHistoryManager);
            this.fallbackMap.set('pagingServiceManager', () => window.OTA.pagingServiceManager || window.pagingServiceManager);
            this.fallbackMap.set('languageManager', () => window.OTA.languageManager || window.languageManager);
            this.fallbackMap.set('kimiService', () => window.OTA.kimiService || window.kimiService);
            
            // P0优先级：核心架构服务
            this.fallbackMap.set('apiKeyManager', () => window.OTA.apiKeyManager || window.apiKeyManager);
            this.fallbackMap.set('warningManager', () => window.OTA.core?.warningManager || window.warningManager);
            this.fallbackMap.set('configManager', () => window.OTA.gemini?.core?.configManager || window.configManager);
            this.fallbackMap.set('componentLifecycleManager', () => window.OTA.componentLifecycleManager || window.componentLifecycleManager);
            this.fallbackMap.set('unifiedDataManager', () => window.OTA.unifiedDataManager || window.unifiedDataManager);
        }

        /**
         * 注册核心服务到依赖容器
         */
        registerCoreServices() {
            if (!this.container) return;

            // 注册核心服务工厂函数
            this.container.register('appState', () => {
                return window.OTA.appState || window.appState || this.createAppState();
            });

            this.container.register('logger', () => {
                return window.OTA.logger || window.logger || this.createLogger();
            });

            this.container.register('apiService', () => {
                return window.OTA.apiService || window.apiService || this.createApiService();
            });

            this.container.register('geminiService', () => {
                // 先检查已注册的实例
                if (window.OTA.geminiService) {
                    return window.OTA.geminiService;
                }
                // 尝试从coordinator获取
                if (window.OTA.gemini?.coordinator) {
                    return window.OTA.gemini.coordinator;
                }
                // 最后降级到默认实例
                return this.createGeminiService();
            });

            this.container.register('uiManager', () => {
                return window.OTA.uiManager || window.uiManager || this.createUIManager();
            });

            this.container.register('utils', () => {
                return window.OTA.utils || window.utils || this.createUtils();
            });

            this.container.register('formManager', () => {
                return window.OTA.formManager || window.formManager || this.createFormManager();
            });

            this.container.register('languageManager', () => {
                return window.OTA.languageManager || window.getLanguageManager?.() || this.createLanguageManager();
            });

            this.container.register('kimiService', () => {
                return window.OTA.kimiService || window.getKimiService?.() || this.createKimiService();
            });

            this.container.register('imageUploadManager', () => {
                return window.OTA.imageUploadManager || window.getImageUploadManager?.() || this.createImageUploadManager();
            });

            this.container.register('currencyConverter', () => {
                return window.OTA.currencyConverter || window.getCurrencyConverter?.() || this.createCurrencyConverter();
            });

            this.container.register('multiOrderManager', () => {
                return window.OTA.multiOrderManager || window.getMultiOrderManager?.() || this.createMultiOrderManager();
            });

            this.container.register('orderHistoryManager', () => {
                return window.OTA.orderHistoryManager || window.getOrderHistoryManager?.() || this.createOrderHistoryManager();
            });

            this.container.register('pagingServiceManager', () => {
                return window.OTA.pagingServiceManager || window.getPagingServiceManager?.() || this.createPagingServiceManager();
            });

            this.container.register('realtimeAnalysisManager', () => {
                return window.OTA.realtimeAnalysisManager || window.getRealtimeAnalysisManager?.() || this.createRealtimeAnalysisManager();
            });

            this.container.register('i18nManager', () => {
                return window.OTA.i18nManager || window.getI18nManager?.() || this.createI18nManager();
            });

            // P0优先级：核心架构服务
            this.container.register('apiKeyManager', () => {
                return window.OTA.apiKeyManager || window.getApiKeyManager?.() || this.createApiKeyManager();
            });

            this.container.register('warningManager', () => {
                return window.OTA.core?.warningManager || window.getWarningManager?.() || this.createWarningManager();
            });

            this.container.register('configManager', () => {
                return window.OTA.gemini?.core?.configManager || 
                       window.OTA.gemini?.core?.getConfigManager?.() || 
                       this.createConfigManager();
            });

            this.container.register('componentLifecycleManager', () => {
                return window.OTA.componentLifecycleManager || this.createComponentLifecycleManager();
            });

            this.container.register('unifiedDataManager', () => {
                return window.OTA.unifiedDataManager || this.createUnifiedDataManager();
            });
        }

        /**
         * 获取服务实例
         * @param {string} serviceName - 服务名称
         * @returns {any} 服务实例
         */
        getService(serviceName) {
            // 记录服务使用统计
            this.recordServiceUsage(serviceName);
            
            // 优先从依赖容器获取
            if (this.container && this.container.has(serviceName)) {
                try {
                    const service = this.container.get(serviceName);
                    this.recordServiceAccess(serviceName, 'container');
                    return service;
                } catch (error) {
                    console.warn(`从依赖容器获取 ${serviceName} 失败，尝试降级方案:`, error.message);
                    this.recordServiceAccess(serviceName, 'container_error');
                }
            }

            // 降级到旧的获取方式
            if (this.fallbackMap.has(serviceName)) {
                const service = this.fallbackMap.get(serviceName)();
                this.recordServiceAccess(serviceName, 'fallback');
                this.recordFallbackUsage(serviceName);
                
                // 使用智能警告管理器发出迁移警告
                if (!this.migrationWarnings.has(serviceName)) {
                    const warningManager = window.OTA?.core?.warningManager;
                    if (warningManager) {
                        warningManager.warn(
                            'SERVICE_FALLBACK',
                            `服务 ${serviceName} 使用了降级获取方式，建议迁移到依赖容器`,
                            'WARNING',
                            { serviceName, fallbackUsed: true }
                        );
                    } else {
                        // 降级到原有警告方式
                        console.warn(`⚠️ 服务 ${serviceName} 使用了降级获取方式，建议迁移到依赖容器`);
                    }
                    this.migrationWarnings.add(serviceName);
                }
                
                return service;
            }

            // 最后尝试直接从全局获取
            let globalService = window.OTA[serviceName] || window[serviceName];

            // 🔧 修复重构遗留问题：支持嵌套路径查找
            // 处理 configManager 等服务的嵌套路径暴露
            if (!globalService && serviceName === 'configManager') {
                globalService = window.OTA?.gemini?.core?.configManager ||
                               window.OTA?.gemini?.core?.getConfigManager?.();
            }

            if (globalService) {
                this.recordServiceAccess(serviceName, 'global');
                
                const warningManager = window.OTA?.core?.warningManager;
                if (warningManager) {
                    warningManager.warn(
                        'SERVICE_GLOBAL_ACCESS',
                        `服务 ${serviceName} 从全局获取，建议注册到依赖容器`,
                        'WARNING',
                        { serviceName, globalAccess: true }
                    );
                } else {
                    // 降级到原有警告方式
                    console.warn(`⚠️ 服务 ${serviceName} 从全局获取，建议注册到依赖容器`);
                }
                return globalService;
            }

            this.recordServiceAccess(serviceName, 'not_found');

            throw new Error(`服务 ${serviceName} 未找到`);
        }

        /**
         * 检查服务是否可用
         * @param {string} serviceName - 服务名称
         * @returns {boolean}
         */
        hasService(serviceName) {
            try {
                const service = this.getService(serviceName);
                return !!service;
            } catch {
                return false;
            }
        }

        /**
         * 获取所有可用服务
         * @returns {string[]}
         */
        getAvailableServices() {
            const services = new Set();
            
            // 从依赖容器获取
            if (this.container) {
                this.container.getRegisteredServices().forEach(name => services.add(name));
            }
            
            // 从降级映射获取
            this.fallbackMap.forEach((_, name) => services.add(name));
            
            // 从全局获取
            Object.keys(window.OTA || {}).forEach(name => services.add(name));
            
            return Array.from(services);
        }

        /**
         * 创建默认的AppState实例
         */
        createAppState() {
            console.warn('创建默认AppState实例，建议使用正式的工厂函数');
            return new (window.AppState || class DefaultAppState {
                constructor() {
                    this.state = {};
                }
                get(path) { return path.split('.').reduce((obj, key) => obj?.[key], this.state); }
                set(path, value) { 
                    const keys = path.split('.');
                    const lastKey = keys.pop();
                    const target = keys.reduce((obj, key) => obj[key] = obj[key] || {}, this.state);
                    target[lastKey] = value;
                }
            })();
        }

        /**
         * 创建默认的Logger实例
         */
        createLogger() {
            // 移除警告日志：创建默认Logger实例的提示
            return {
                log: (message, level = 'info', data = null) => {
                    const levelStr = typeof level === 'string' ? level : 'info';
                    console.log(`[${levelStr.toUpperCase()}] ${message}`, data || '');
                },
                logError: (message, error) => {
                    console.error(`[ERROR] ${message}`, error);
                }
            };
        }

        /**
         * 创建默认的ApiService实例
         */
        createApiService() {
            // 移除警告日志：创建默认ApiService实例的提示
            return {
                isAvailable: () => false,
                createOrder: () => Promise.reject(new Error('ApiService not properly initialized'))
            };
        }

        /**
         * 创建默认的GeminiService实例
         */
        createGeminiService() {
            return {
                isAvailable: () => false,
                analyzeOrder: () => Promise.reject(new Error('GeminiService not properly initialized'))
            };
        }

        /**
         * 创建默认的UIManager实例
         */
        createUIManager() {
            console.warn('创建默认UIManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getManager: () => null
            };
        }

        /**
         * 创建默认的Utils实例
         */
        createUtils() {
            console.warn('创建默认Utils实例，建议使用正式的工厂函数');
            return {
                debounce: (fn, delay) => fn,
                throttle: (fn, delay) => fn,
                formatDate: (date) => date.toString()
            };
        }

        /**
         * 创建默认的FormManager实例
         */
        createFormManager() {
            console.warn('创建默认FormManager实例，建议使用正式的工厂函数');
            return {
                setLanguageSelection: (languageIds) => {
                    console.log('FormManager (stub): 设置语言选择', languageIds);
                },
                init: () => {},
                validate: () => true
            };
        }

        /**
         * 创建默认的LanguageManager实例
         */
        createLanguageManager() {
            console.warn('创建默认LanguageManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getCurrentLanguage: () => 'en',
                setLanguage: (lang) => console.log('LanguageManager: 设置语言', lang),
                getAvailableLanguages: () => []
            };
        }

        /**
         * 创建默认的KimiService实例
         */
        createKimiService() {
            console.warn('创建默认KimiService实例，建议使用正式的工厂函数');
            return {
                isAvailable: () => false,
                analyzeOrder: () => Promise.reject(new Error('KimiService not properly initialized')),
                init: () => {}
            };
        }

        /**
         * 创建默认的ImageUploadManager实例
         */
        createImageUploadManager() {
            console.warn('创建默认ImageUploadManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                upload: () => Promise.reject(new Error('ImageUploadManager not properly initialized')),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的CurrencyConverter实例
         */
        createCurrencyConverter() {
            console.warn('创建默认CurrencyConverter实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                convertToMYR: (amount, currency) => ({ 
                    originalAmount: amount, 
                    convertedAmount: amount, 
                    needsConversion: false 
                }),
                formatPrice: (amount, currency) => `${currency} ${amount}`,
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的MultiOrderManager实例
         */
        createMultiOrderManager() {
            console.warn('创建默认MultiOrderManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                processMultipleOrders: () => Promise.reject(new Error('MultiOrderManager not properly initialized')),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的OrderHistoryManager实例
         */
        createOrderHistoryManager() {
            console.warn('创建默认OrderHistoryManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getOrderHistory: () => Promise.reject(new Error('OrderHistoryManager not properly initialized')),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的PagingServiceManager实例
         */
        createPagingServiceManager() {
            console.warn('创建默认PagingServiceManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                paginate: () => Promise.reject(new Error('PagingServiceManager not properly initialized')),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的RealtimeAnalysisManager实例
         */
        createRealtimeAnalysisManager() {
            console.warn('创建默认RealtimeAnalysisManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                triggerRealtimeAnalysis: () => Promise.resolve([]),
                setRealtimeAnalysisEnabled: () => {},
                getRealtimeAnalysisStatus: () => ({ enabled: false, isAnalyzing: false }),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的I18nManager实例
         */
        createI18nManager() {
            console.warn('创建默认I18nManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                t: (key) => key,
                getCurrentLanguage: () => 'en',
                setLanguage: (lang) => console.log('I18nManager: 设置语言', lang),
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的ApiKeyManager实例
         */
        createApiKeyManager() {
            console.warn('创建默认ApiKeyManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getApiKey: (service) => null,
                setApiKey: (service, key) => console.log('ApiKeyManager: 设置API密钥', service),
                hasApiKey: (service) => false,
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的WarningManager实例
         */
        createWarningManager() {
            console.warn('创建默认WarningManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                warn: (type, message, level, data) => console.warn(`[${level}] ${type}: ${message}`, data),
                isAvailable: () => false,
                getWarningStats: () => ({ total: 0, byType: {} })
            };
        }

        /**
         * 创建默认的ConfigManager实例
         */
        createConfigManager() {
            console.warn('创建默认ConfigManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getConfig: (key, defaultValue) => defaultValue,
                setConfig: (key, value) => console.log('ConfigManager: 设置配置', key, value),
                hasConfig: (key) => false,
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的ComponentLifecycleManager实例
         */
        createComponentLifecycleManager() {
            console.warn('创建默认ComponentLifecycleManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                register: (component) => console.log('ComponentLifecycleManager: 注册组件', component),
                unregister: (component) => console.log('ComponentLifecycleManager: 注销组件', component),
                getComponents: () => [],
                isAvailable: () => false
            };
        }

        /**
         * 创建默认的UnifiedDataManager实例
         */
        createUnifiedDataManager() {
            console.warn('创建默认UnifiedDataManager实例，建议使用正式的工厂函数');
            return {
                init: () => {},
                getData: (key) => null,
                setData: (key, value) => console.log('UnifiedDataManager: 设置数据', key, value),
                hasData: (key) => false,
                clearData: () => console.log('UnifiedDataManager: 清理数据'),
                isAvailable: () => false
            };
        }

        /**
         * 记录服务使用统计
         * @param {string} serviceName - 服务名称
         */
        recordServiceUsage(serviceName) {
            if (!this.monitoringEnabled) return;
            
            const current = this.serviceUsageStats.get(serviceName) || 0;
            this.serviceUsageStats.set(serviceName, current + 1);
        }

        /**
         * 记录服务访问方式
         * @param {string} serviceName - 服务名称
         * @param {string} accessType - 访问类型: container, fallback, global, not_found, container_error
         */
        recordServiceAccess(serviceName, accessType) {
            if (!this.monitoringEnabled) return;
            
            if (!this.serviceAccessTimes.has(serviceName)) {
                this.serviceAccessTimes.set(serviceName, {
                    container: 0,
                    fallback: 0,
                    global: 0,
                    not_found: 0,
                    container_error: 0
                });
            }
            
            const stats = this.serviceAccessTimes.get(serviceName);
            stats[accessType] = (stats[accessType] || 0) + 1;
        }

        /**
         * 记录降级使用次数
         * @param {string} serviceName - 服务名称
         */
        recordFallbackUsage(serviceName) {
            if (!this.monitoringEnabled) return;
            
            const current = this.fallbackUsageCount.get(serviceName) || 0;
            this.fallbackUsageCount.set(serviceName, current + 1);
        }

        /**
         * 获取服务使用统计
         * @returns {Object}
         */
        getServiceUsageStats() {
            return {
                totalRequests: Array.from(this.serviceUsageStats.values()).reduce((sum, count) => sum + count, 0),
                serviceUsage: Object.fromEntries(this.serviceUsageStats),
                accessPatterns: Object.fromEntries(this.serviceAccessTimes),
                fallbackUsage: Object.fromEntries(this.fallbackUsageCount),
                topServices: Array.from(this.serviceUsageStats.entries())
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 10)
                    .map(([name, count]) => ({ name, count })),
                healthScore: this.calculateServiceHealthScore()
            };
        }

        /**
         * 计算服务健康分数
         * @returns {number} 0-100的分数
         */
        calculateServiceHealthScore() {
            const totalRequests = Array.from(this.serviceUsageStats.values()).reduce((sum, count) => sum + count, 0);
            if (totalRequests === 0) return 100;

            let containerRequests = 0;
            let fallbackRequests = 0;
            let globalRequests = 0;
            let errorRequests = 0;

            for (const [serviceName, stats] of this.serviceAccessTimes) {
                containerRequests += stats.container || 0;
                fallbackRequests += stats.fallback || 0;
                globalRequests += stats.global || 0;
                errorRequests += stats.container_error || 0;
                errorRequests += stats.not_found || 0;
            }

            const containerRatio = containerRequests / totalRequests;
            const fallbackRatio = fallbackRequests / totalRequests;
            const globalRatio = globalRequests / totalRequests;
            const errorRatio = errorRequests / totalRequests;

            // 健康分数计算：容器访问得满分，降级扣分，全局访问扣更多分，错误扣最多分
            const score = 100 * containerRatio + 
                         70 * fallbackRatio + 
                         50 * globalRatio + 
                         0 * errorRatio;

            return Math.round(score);
        }

        /**
         * 重置监控数据
         */
        resetMonitoringData() {
            this.serviceUsageStats.clear();
            this.serviceAccessTimes.clear();
            this.fallbackUsageCount.clear();
            console.log('🔄 监控数据已重置');
        }

        /**
         * 启用/禁用监控
         * @param {boolean} enabled - 是否启用
         */
        setMonitoringEnabled(enabled) {
            this.monitoringEnabled = enabled;
            console.log(`📊 服务监控已${enabled ? '启用' : '禁用'}`);
        }

        /**
         * 获取迁移状态报告
         * @returns {Object}
         */
        getMigrationReport() {
            const usageStats = this.getServiceUsageStats();
            
            return {
                totalServices: this.getAvailableServices().length,
                containerServices: this.container ? this.container.getRegisteredServices().length : 0,
                fallbackUsed: this.migrationWarnings.size,
                warnings: Array.from(this.migrationWarnings),
                usageStats: usageStats,
                recommendations: [
                    '将所有服务注册到依赖容器',
                    '替换直接的全局访问为 getService() 调用',
                    '移除双重获取模式 (window.OTA.xxx || window.xxx)',
                    `当前健康分数: ${usageStats.healthScore}/100`
                ]
            };
        }
    }

    // 创建全局服务定位器实例
    const serviceLocator = new ServiceLocator();

    // 等待依赖容器准备就绪后初始化
    if (window.OTA.container) {
        serviceLocator.init(window.OTA.container);
    } else {
        // 延迟初始化
        setTimeout(() => {
            if (window.OTA.container) {
                serviceLocator.init(window.OTA.container);
            }
        }, 100);
    }

    // 暴露到OTA命名空间
    window.OTA.serviceLocator = serviceLocator;

    // 提供统一的服务获取函数
    window.OTA.getService = function(serviceName) {
        return serviceLocator.getService(serviceName);
    };

    // 向后兼容的全局函数
    window.getService = window.OTA.getService;

    // 暴露监控功能到全局
    window.OTA.getServiceUsageStats = function() {
        return serviceLocator.getServiceUsageStats();
    };

    window.OTA.getServiceMigrationReport = function() {
        return serviceLocator.getMigrationReport();
    };

    window.OTA.resetServiceMonitoring = function() {
        return serviceLocator.resetMonitoringData();
    };

    window.OTA.setServiceMonitoringEnabled = function(enabled) {
        return serviceLocator.setMonitoringEnabled(enabled);
    };

    // 提供便捷的服务获取函数（仅提供没有专门文件的服务）
    // 注意：有专门服务文件的函数（如getAppState, getLogger, getAPIService等）由各自文件负责定义
    window.getApiService = () => serviceLocator.getService('apiService'); // getApiService是getAPIService的别名
    window.getGeminiService = () => serviceLocator.getService('geminiService');
    window.getUIManager = () => serviceLocator.getService('uiManager');
    window.getUtils = () => serviceLocator.getService('utils');
    window.getImageUploadManager = () => serviceLocator.getService('imageUploadManager');
    window.getCurrencyConverter = () => serviceLocator.getService('currencyConverter');
    window.getMultiOrderManager = () => serviceLocator.getService('multiOrderManager');
    window.getOrderHistoryManager = () => serviceLocator.getService('orderHistoryManager');
    window.getPagingServiceManager = () => serviceLocator.getService('pagingServiceManager');
    window.getKimiService = () => serviceLocator.getService('kimiService');

    console.log('✅ 服务定位器已加载');

})();
