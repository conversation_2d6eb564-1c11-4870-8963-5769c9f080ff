<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能学习型格式预处理引擎 - 用户反馈收集系统</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .feedback-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .feedback-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .feedback-header h1 {
            color: #4a5568;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .feedback-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #e2e8f0;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: #718096;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-button:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-section {
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(247, 250, 252, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4a5568;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .rating-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .rating-star {
            font-size: 2rem;
            color: #e2e8f0;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .rating-star:hover,
        .rating-star.active {
            color: #fbbf24;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .feedback-summary {
            background: rgba(72, 187, 120, 0.1);
            border: 1px solid rgba(72, 187, 120, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(72, 187, 120, 0.2);
        }

        .summary-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            transition: width 0.3s ease;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .alert-success {
            background: rgba(72, 187, 120, 0.1);
            border-color: #48bb78;
            color: #2f855a;
        }

        .alert-info {
            background: rgba(66, 153, 225, 0.1);
            border-color: #4299e1;
            color: #2b6cb0;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .feedback-tabs {
                flex-direction: column;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
            
            .rating-group {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="feedback-container">
        <div class="feedback-header">
            <h1>用户反馈收集系统</h1>
            <p>您的反馈对我们改进智能学习型格式预处理引擎非常重要</p>
        </div>

        <div class="feedback-tabs">
            <button class="tab-button active" onclick="switchTab('general')">一般反馈</button>
            <button class="tab-button" onclick="switchTab('bug')">问题报告</button>
            <button class="tab-button" onclick="switchTab('feature')">功能建议</button>
            <button class="tab-button" onclick="switchTab('training')">培训反馈</button>
        </div>

        <!-- 一般反馈标签页 -->
        <div id="general-tab" class="tab-content active">
            <form id="general-feedback-form">
                <div class="form-section">
                    <div class="section-title">
                        📝 基本信息
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">姓名</label>
                        <input type="text" class="form-input" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">部门</label>
                        <select class="form-select" name="department" required>
                            <option value="">请选择部门</option>
                            <option value="operations">运营部</option>
                            <option value="customer-service">客服部</option>
                            <option value="sales">销售部</option>
                            <option value="management">管理层</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">使用经验</label>
                        <select class="form-select" name="experience" required>
                            <option value="">请选择</option>
                            <option value="new">新用户（少于1周）</option>
                            <option value="beginner">初级用户（1周-1个月）</option>
                            <option value="intermediate">中级用户（1-3个月）</option>
                            <option value="advanced">高级用户（3个月以上）</option>
                        </select>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        ⭐ 整体评价
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">系统整体满意度</label>
                        <div class="rating-group">
                            <span class="rating-star" data-rating="1">★</span>
                            <span class="rating-star" data-rating="2">★</span>
                            <span class="rating-star" data-rating="3">★</span>
                            <span class="rating-star" data-rating="4">★</span>
                            <span class="rating-star" data-rating="5">★</span>
                            <span style="margin-left: 15px; color: #718096;">点击星星评分</span>
                        </div>
                        <input type="hidden" name="overall_rating" value="">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">易用性评分</label>
                        <div class="rating-group">
                            <span class="rating-star" data-rating="1" data-field="usability">★</span>
                            <span class="rating-star" data-rating="2" data-field="usability">★</span>
                            <span class="rating-star" data-rating="3" data-field="usability">★</span>
                            <span class="rating-star" data-rating="4" data-field="usability">★</span>
                            <span class="rating-star" data-rating="5" data-field="usability">★</span>
                        </div>
                        <input type="hidden" name="usability_rating" value="">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">学习效果评分</label>
                        <div class="rating-group">
                            <span class="rating-star" data-rating="1" data-field="learning">★</span>
                            <span class="rating-star" data-rating="2" data-field="learning">★</span>
                            <span class="rating-star" data-rating="3" data-field="learning">★</span>
                            <span class="rating-star" data-rating="4" data-field="learning">★</span>
                            <span class="rating-star" data-rating="5" data-field="learning">★</span>
                        </div>
                        <input type="hidden" name="learning_rating" value="">
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        💭 详细反馈
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">您最喜欢系统的哪些功能？</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" name="liked_features" value="auto_learning">
                                <label>自动学习功能</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" name="liked_features" value="smart_suggestions">
                                <label>智能建议</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" name="liked_features" value="multi_order">
                                <label>多订单处理</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" name="liked_features" value="dashboard">
                                <label>管理面板</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" name="liked_features" value="performance">
                                <label>系统性能</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" name="liked_features" value="ui_design">
                                <label>界面设计</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">您认为哪些方面需要改进？</label>
                        <textarea class="form-textarea" name="improvements" placeholder="请详细描述您认为需要改进的地方..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">其他建议或意见</label>
                        <textarea class="form-textarea" name="additional_comments" placeholder="请分享您的其他想法和建议..."></textarea>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button type="submit" class="btn btn-primary">提交反馈</button>
                    <button type="reset" class="btn btn-secondary" style="margin-left: 15px;">重置表单</button>
                </div>
            </form>
        </div>

        <!-- 问题报告标签页 -->
        <div id="bug-tab" class="tab-content">
            <form id="bug-report-form">
                <div class="form-section">
                    <div class="section-title">
                        🐛 问题详情
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">问题类型</label>
                        <select class="form-select" name="bug_type" required>
                            <option value="">请选择问题类型</option>
                            <option value="system_error">系统错误</option>
                            <option value="performance">性能问题</option>
                            <option value="ui_issue">界面问题</option>
                            <option value="data_processing">数据处理错误</option>
                            <option value="learning_issue">学习功能问题</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">严重程度</label>
                        <select class="form-select" name="severity" required>
                            <option value="">请选择严重程度</option>
                            <option value="critical">严重（系统无法使用）</option>
                            <option value="high">高（影响主要功能）</option>
                            <option value="medium">中等（影响部分功能）</option>
                            <option value="low">低（轻微影响）</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">问题描述</label>
                        <textarea class="form-textarea" name="bug_description" required placeholder="请详细描述遇到的问题，包括具体的错误信息..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">重现步骤</label>
                        <textarea class="form-textarea" name="reproduction_steps" placeholder="请描述如何重现这个问题的具体步骤..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">期望结果</label>
                        <textarea class="form-textarea" name="expected_result" placeholder="请描述您期望的正确结果..."></textarea>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        🖥️ 环境信息
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">浏览器</label>
                        <input type="text" class="form-input" name="browser" id="browser-info" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">操作系统</label>
                        <input type="text" class="form-input" name="os" id="os-info" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">屏幕分辨率</label>
                        <input type="text" class="form-input" name="screen_resolution" id="screen-info" readonly>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button type="submit" class="btn btn-primary">提交问题报告</button>
                    <button type="reset" class="btn btn-secondary" style="margin-left: 15px;">重置表单</button>
                </div>
            </form>
        </div>

        <!-- 功能建议标签页 -->
        <div id="feature-tab" class="tab-content">
            <form id="feature-request-form">
                <div class="form-section">
                    <div class="section-title">
                        💡 功能建议
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">建议类型</label>
                        <select class="form-select" name="feature_type" required>
                            <option value="">请选择建议类型</option>
                            <option value="new_feature">新功能</option>
                            <option value="enhancement">功能增强</option>
                            <option value="integration">系统集成</option>
                            <option value="ui_improvement">界面改进</option>
                            <option value="performance">性能优化</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">优先级</label>
                        <select class="form-select" name="priority" required>
                            <option value="">请选择优先级</option>
                            <option value="high">高（急需）</option>
                            <option value="medium">中（重要）</option>
                            <option value="low">低（建议）</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">功能描述</label>
                        <textarea class="form-textarea" name="feature_description" required placeholder="请详细描述您建议的功能..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">使用场景</label>
                        <textarea class="form-textarea" name="use_case" placeholder="请描述这个功能的具体使用场景..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">预期收益</label>
                        <textarea class="form-textarea" name="expected_benefit" placeholder="请描述这个功能能带来什么好处..."></textarea>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button type="submit" class="btn btn-primary">提交功能建议</button>
                    <button type="reset" class="btn btn-secondary" style="margin-left: 15px;">重置表单</button>
                </div>
            </form>
        </div>

        <!-- 培训反馈标签页 -->
        <div id="training-tab" class="tab-content">
            <form id="training-feedback-form">
                <div class="form-section">
                    <div class="section-title">
                        🎓 培训评价
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">培训内容评分</label>
                        <div class="rating-group">
                            <span class="rating-star" data-rating="1" data-field="training_content">★</span>
                            <span class="rating-star" data-rating="2" data-field="training_content">★</span>
                            <span class="rating-star" data-rating="3" data-field="training_content">★</span>
                            <span class="rating-star" data-rating="4" data-field="training_content">★</span>
                            <span class="rating-star" data-rating="5" data-field="training_content">★</span>
                        </div>
                        <input type="hidden" name="training_content_rating" value="">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">培训讲师评分</label>
                        <div class="rating-group">
                            <span class="rating-star" data-rating="1" data-field="trainer">★</span>
                            <span class="rating-star" data-rating="2" data-field="trainer">★</span>
                            <span class="rating-star" data-rating="3" data-field="trainer">★</span>
                            <span class="rating-star" data-rating="4" data-field="trainer">★</span>
                            <span class="rating-star" data-rating="5" data-field="trainer">★</span>
                        </div>
                        <input type="hidden" name="trainer_rating" value="">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">培训材料评分</label>
                        <div class="rating-group">
                            <span class="rating-star" data-rating="1" data-field="materials">★</span>
                            <span class="rating-star" data-rating="2" data-field="materials">★</span>
                            <span class="rating-star" data-rating="3" data-field="materials">★</span>
                            <span class="rating-star" data-rating="4" data-field="materials">★</span>
                            <span class="rating-star" data-rating="5" data-field="materials">★</span>
                        </div>
                        <input type="hidden" name="materials_rating" value="">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">培训时长是否合适？</label>
                        <select class="form-select" name="training_duration">
                            <option value="">请选择</option>
                            <option value="too_short">太短</option>
                            <option value="appropriate">合适</option>
                            <option value="too_long">太长</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">培训后是否能独立使用系统？</label>
                        <select class="form-select" name="independence_level">
                            <option value="">请选择</option>
                            <option value="fully_independent">完全独立</option>
                            <option value="mostly_independent">基本独立</option>
                            <option value="need_some_help">需要一些帮助</option>
                            <option value="need_significant_help">需要很多帮助</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">培训改进建议</label>
                        <textarea class="form-textarea" name="training_improvements" placeholder="请提出培训方面的改进建议..."></textarea>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button type="submit" class="btn btn-primary">提交培训反馈</button>
                    <button type="reset" class="btn btn-secondary" style="margin-left: 15px;">重置表单</button>
                </div>
            </form>
        </div>

        <!-- 反馈提交成功提示 -->
        <div id="success-message" class="alert alert-success hidden">
            <strong>感谢您的反馈！</strong> 您的意见对我们非常重要，我们会认真考虑并持续改进系统。
        </div>
    </div>

    <script src="feedback-collection.js"></script>
</body>
</html>
