<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习存储管理器测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>学习存储管理器测试</h1>
    
    <div class="test-section">
        <h2>测试结果</h2>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>存储使用情况</h2>
        <div id="storageUsage"></div>
    </div>

    <div class="test-section">
        <h2>数据结构测试</h2>
        <div id="dataStructureTest"></div>
    </div>

    <!-- 加载必要的模块 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/learning-engine/learning-config.js"></script>
    <script src="js/learning-engine/learning-storage-manager.js"></script>

    <script>
        // 测试函数
        function runTests() {
            const results = document.getElementById('testResults');
            const storageDiv = document.getElementById('storageUsage');
            const dataStructureDiv = document.getElementById('dataStructureTest');
            
            try {
                // 测试1: 检查模块加载
                results.innerHTML += '<div class="info">测试1: 检查模块加载</div>';
                
                if (window.OTA && window.OTA.learningConfig) {
                    results.innerHTML += '<div class="success">✅ LearningConfig 加载成功</div>';
                } else {
                    results.innerHTML += '<div class="error">❌ LearningConfig 加载失败</div>';
                }
                
                if (window.OTA && window.OTA.learningStorageManager) {
                    results.innerHTML += '<div class="success">✅ LearningStorageManager 加载成功</div>';
                } else {
                    results.innerHTML += '<div class="error">❌ LearningStorageManager 加载失败</div>';
                }

                // 测试2: 获取存储管理器实例
                results.innerHTML += '<div class="info">测试2: 获取存储管理器实例</div>';
                const storageManager = getLearningStorageManager();
                
                if (storageManager) {
                    results.innerHTML += '<div class="success">✅ 存储管理器实例获取成功</div>';
                    results.innerHTML += `<div class="info">版本: ${storageManager.version}</div>`;
                } else {
                    results.innerHTML += '<div class="error">❌ 存储管理器实例获取失败</div>';
                    return;
                }

                // 测试3: 存储使用情况
                results.innerHTML += '<div class="info">测试3: 存储使用情况</div>';
                const usage = storageManager.getStorageUsage();
                storageDiv.innerHTML = `<pre>${JSON.stringify(usage, null, 2)}</pre>`;
                results.innerHTML += '<div class="success">✅ 存储使用情况获取成功</div>';

                // 测试4: 数据结构定义
                results.innerHTML += '<div class="info">测试4: 数据结构定义</div>';
                if (window.OTA.LearningDataStructures) {
                    results.innerHTML += '<div class="success">✅ 数据结构定义加载成功</div>';
                    dataStructureDiv.innerHTML = `<pre>${JSON.stringify(window.OTA.LearningDataStructures, null, 2)}</pre>`;
                } else {
                    results.innerHTML += '<div class="error">❌ 数据结构定义加载失败</div>';
                }

                // 测试5: 基本存储操作
                results.innerHTML += '<div class="info">测试5: 基本存储操作</div>';
                
                // 测试数据设置
                const testData = {
                    test: true,
                    timestamp: new Date().toISOString(),
                    data: { message: 'Hello Learning System' }
                };
                
                const testKey = 'ota-learning-test';
                const setResult = storageManager.setData(testKey, testData);
                
                if (setResult) {
                    results.innerHTML += '<div class="success">✅ 数据设置成功</div>';
                } else {
                    results.innerHTML += '<div class="error">❌ 数据设置失败</div>';
                }

                // 测试数据获取
                const retrievedData = storageManager.getData(testKey);
                if (retrievedData && retrievedData.test === true) {
                    results.innerHTML += '<div class="success">✅ 数据获取成功</div>';
                } else {
                    results.innerHTML += '<div class="error">❌ 数据获取失败</div>';
                }

                // 清理测试数据
                storageManager.removeData(testKey);
                results.innerHTML += '<div class="info">测试数据已清理</div>';

                // 测试6: 导出功能
                results.innerHTML += '<div class="info">测试6: 数据导出功能</div>';
                const exportData = storageManager.exportAllData();
                if (exportData && exportData.version) {
                    results.innerHTML += '<div class="success">✅ 数据导出成功</div>';
                    results.innerHTML += `<div class="info">导出数据版本: ${exportData.version}</div>`;
                } else {
                    results.innerHTML += '<div class="error">❌ 数据导出失败</div>';
                }

                results.innerHTML += '<div class="success"><strong>所有测试完成！</strong></div>';

            } catch (error) {
                results.innerHTML += `<div class="error">测试过程中发生错误: ${error.message}</div>`;
                console.error('测试错误:', error);
            }
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runTests, 100); // 稍微延迟以确保所有模块加载完成
        });
    </script>
</body>
</html>
