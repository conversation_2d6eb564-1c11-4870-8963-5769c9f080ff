<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接口修复验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>接口修复验证测试</h1>
    <div id="results"></div>

    <!-- 只加载必要的依赖 -->
    <script src="js/services/logger.js"></script>
    <script src="js/core/dependency-container.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function runInterfaceTests() {
            addResult('开始接口修复验证测试...', 'info');

            // 测试 DependencyContainer 接口
            addResult('=== 测试 DependencyContainer 接口 ===', 'info');
            
            const container = window.OTA?.container;
            if (!container) {
                addResult('❌ DependencyContainer 不存在', 'error');
                return;
            }

            // 测试必需方法
            const containerMethods = ['isInitialized', 'getStatus', 'register', 'resolve'];
            let containerSuccess = true;

            containerMethods.forEach(method => {
                if (typeof container[method] === 'function') {
                    addResult(`✅ container.${method} 方法存在`, 'success');
                } else {
                    addResult(`❌ container.${method} 方法缺失`, 'error');
                    containerSuccess = false;
                }
            });

            // 测试 resolve 方法功能
            try {
                // 注册一个测试服务
                container.register('testService', () => ({ test: true }));
                const resolved = container.resolve('testService');
                if (resolved && resolved.test === true) {
                    addResult('✅ resolve 方法功能正常', 'success');
                } else {
                    addResult('❌ resolve 方法功能异常', 'error');
                    containerSuccess = false;
                }
            } catch (error) {
                addResult(`❌ resolve 方法测试失败: ${error.message}`, 'error');
                containerSuccess = false;
            }

            // 测试 Registry 接口
            addResult('=== 测试 Registry 接口 ===', 'info');
            
            const Registry = window.OTA?.Registry;
            if (!Registry) {
                addResult('❌ Registry 不存在', 'error');
                return;
            }

            const registryMethods = ['getRegistryInfo', 'register', 'unregister'];
            let registrySuccess = true;

            registryMethods.forEach(method => {
                if (typeof Registry[method] === 'function') {
                    addResult(`✅ Registry.${method} 方法存在`, 'success');
                } else {
                    addResult(`❌ Registry.${method} 方法缺失`, 'error');
                    registrySuccess = false;
                }
            });

            // 测试 Registry register/unregister 功能
            try {
                // 测试注册
                Registry.register('testRegistryService', () => ({ registry: true }));
                
                // 测试获取
                const service = Registry.get('testRegistryService');
                if (service && service.registry === true) {
                    addResult('✅ Registry.register 方法功能正常', 'success');
                } else {
                    addResult('❌ Registry.register 方法功能异常', 'error');
                    registrySuccess = false;
                }

                // 测试注销
                const unregistered = Registry.unregister('testRegistryService');
                if (unregistered === true) {
                    addResult('✅ Registry.unregister 方法功能正常', 'success');
                } else {
                    addResult('❌ Registry.unregister 方法功能异常', 'error');
                    registrySuccess = false;
                }
            } catch (error) {
                addResult(`❌ Registry 方法测试失败: ${error.message}`, 'error');
                registrySuccess = false;
            }

            // 总结
            addResult('=== 测试总结 ===', 'info');
            if (containerSuccess && registrySuccess) {
                addResult('🎉 所有接口修复验证通过！', 'success');
                addResult('开发检查脚本应该不再报告接口完整性问题', 'success');
            } else {
                addResult('❌ 部分测试失败，需要进一步修复', 'error');
            }
        }

        // 等待页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runInterfaceTests, 1000);
        });
    </script>
</body>
</html>