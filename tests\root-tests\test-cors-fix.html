<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>CORS修复测试</title>
</head>
<body>
    <div id="test-result"></div>
    <script>
        // 测试基本功能是否正常
        console.log('🧪 开始CORS修复测试...');
        
        // 检查核心模块是否可以正常加载
        const testResult = document.getElementById('test-result');
        
        setTimeout(() => {
            let results = [];
            
            // 测试OTA命名空间
            results.push(`OTA命名空间: ${window.OTA ? '✅ 存在' : '❌ 不存在'}`);
            
            // 测试容器
            results.push(`依赖容器: ${window.OTA?.container ? '✅ 存在' : '❌ 不存在'}`);
            
            // 测试服务定位器
            results.push(`服务定位器: ${window.OTA?.serviceLocator ? '✅ 存在' : '❌ 不存在'}`);
            
            // 测试核心服务
            try {
                const logger = window.OTA?.getService?.('logger');
                results.push(`Logger服务: ${logger ? '✅ 可用' : '❌ 不可用'}`);
            } catch (e) {
                results.push(`Logger服务: ❌ 错误 - ${e.message}`);
            }
            
            // 测试应用启动
            results.push(`应用实例: ${window.OTA?.app ? '✅ 已启动' : '❌ 未启动'}`);
            
            testResult.innerHTML = `
                <h2>🧪 CORS修复测试结果</h2>
                <ul>
                    ${results.map(r => `<li>${r}</li>`).join('')}
                </ul>
                <p><strong>如果所有项目都显示✅，说明CORS问题已解决！</strong></p>
                <p>现在可以关闭此测试页面，直接双击 index.html 使用应用。</p>
            `;
            
            console.log('🧪 测试完成:', results);
        }, 3000);
    </script>
    
    <!-- 加载核心依赖以测试 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/bootstrap/application-bootstrap.js"></script>
    <script src="main.js"></script>
</body>
</html>