/**
 * 依赖容器单元测试
 * 防止 container.isInitialized 类似问题的测试用例
 * <AUTHOR> System
 * @created 2025-01-29
 */

(function() {
    'use strict';

    /**
     * 简单的测试框架
     */
    class SimpleTestFramework {
        constructor() {
            this.tests = [];
            this.results = [];
        }

        test(name, testFn) {
            this.tests.push({ name, testFn });
        }

        async run() {
            console.log('🧪 开始运行依赖容器测试...');
            
            for (const { name, testFn } of this.tests) {
                try {
                    await testFn();
                    this.results.push({ name, status: 'PASS' });
                    console.log(`✅ ${name}`);
                } catch (error) {
                    this.results.push({ name, status: 'FAIL', error: error.message });
                    console.error(`❌ ${name}: ${error.message}`);
                }
            }

            this.printSummary();
        }

        printSummary() {
            const passed = this.results.filter(r => r.status === 'PASS').length;
            const failed = this.results.filter(r => r.status === 'FAIL').length;
            
            console.log('\n📊 测试结果汇总:');
            console.log(`   通过: ${passed}`);
            console.log(`   失败: ${failed}`);
            console.log(`   总计: ${this.results.length}`);
        }

        assert(condition, message) {
            if (!condition) {
                throw new Error(message || '断言失败');
            }
        }

        assertEqual(actual, expected, message) {
            if (actual !== expected) {
                throw new Error(message || `期望 ${expected}, 实际 ${actual}`);
            }
        }

        assertType(value, expectedType, message) {
            if (typeof value !== expectedType) {
                throw new Error(message || `期望类型 ${expectedType}, 实际类型 ${typeof value}`);
            }
        }
    }

    // 创建测试实例
    const test = new SimpleTestFramework();

    // 测试用例：依赖容器基本接口
    test.test('依赖容器应该存在', () => {
        test.assert(window.OTA, 'window.OTA 应该存在');
        test.assert(window.OTA.container, 'window.OTA.container 应该存在');
    });

    test.test('依赖容器应该有 isInitialized 方法', () => {
        const container = window.OTA.container;
        test.assertType(container.isInitialized, 'function', 'isInitialized 应该是一个函数');
    });

    test.test('isInitialized 方法应该返回布尔值', () => {
        const container = window.OTA.container;
        const result = container.isInitialized();
        test.assertType(result, 'boolean', 'isInitialized() 应该返回布尔值');
    });

    test.test('依赖容器应该有 initialized 属性', () => {
        const container = window.OTA.container;
        test.assert('initialized' in container, 'container 应该有 initialized 属性');
        test.assertType(container.initialized, 'boolean', 'initialized 应该是布尔值');
    });

    test.test('isInitialized 方法应该与 initialized 属性一致', () => {
        const container = window.OTA.container;
        const methodResult = container.isInitialized();
        const propertyValue = container.initialized;
        test.assertEqual(methodResult, propertyValue, 'isInitialized() 结果应该与 initialized 属性一致');
    });

    test.test('依赖容器应该有 getStatus 方法', () => {
        const container = window.OTA.container;
        test.assertType(container.getStatus, 'function', 'getStatus 应该是一个函数');
    });

    test.test('getStatus 方法应该返回包含初始化状态的对象', () => {
        const container = window.OTA.container;
        const status = container.getStatus();
        test.assertType(status, 'object', 'getStatus() 应该返回对象');
        test.assert('initialized' in status, 'status 对象应该包含 initialized 属性');
        test.assertType(status.initialized, 'boolean', 'status.initialized 应该是布尔值');
    });

    // 测试用例：Registry 接口
    test.test('Registry 应该存在', () => {
        test.assert(window.OTA.Registry, 'window.OTA.Registry 应该存在');
    });

    test.test('Registry 应该有 getRegistryInfo 方法', () => {
        const Registry = window.OTA.Registry;
        test.assertType(Registry.getRegistryInfo, 'function', 'getRegistryInfo 应该是一个函数');
    });

    test.test('getRegistryInfo 方法应该正常执行', () => {
        const Registry = window.OTA.Registry;
        const registryInfo = Registry.getRegistryInfo();
        test.assertType(registryInfo, 'object', 'getRegistryInfo() 应该返回对象');
    });

    // 测试用例：错误场景模拟
    test.test('模拟原始错误场景应该不再发生', () => {
        // 这个测试模拟了原始错误的调用场景
        const container = window.OTA.container;
        
        // 如果 isInitialized 方法不存在，这里会抛出 TypeError
        test.assert(typeof container.isInitialized === 'function', 'isInitialized 方法必须存在');
        
        // 实际调用不应该抛出错误
        let callSucceeded = false;
        try {
            const result = container.isInitialized();
            callSucceeded = true;
        } catch (error) {
            throw new Error(`isInitialized() 调用失败: ${error.message}`);
        }
        
        test.assert(callSucceeded, 'isInitialized() 调用应该成功');
    });

    // 测试用例：接口完整性验证
    test.test('依赖容器接口完整性验证', () => {
        const container = window.OTA.container;
        const requiredMethods = ['register', 'resolve', 'isInitialized', 'getStatus'];
        const requiredProperties = ['initialized'];

        // 验证必需方法
        requiredMethods.forEach(methodName => {
            test.assertType(container[methodName], 'function', `${methodName} 方法应该存在`);
        });

        // 验证必需属性
        requiredProperties.forEach(propName => {
            test.assert(propName in container, `${propName} 属性应该存在`);
        });
    });

    // 导出测试框架到全局命名空间
    window.OTA = window.OTA || {};
    window.OTA.tests = window.OTA.tests || {};
    window.OTA.tests.dependencyContainerTest = test;

    // 自动运行测试（如果在测试环境中）
    if (window.location.search.includes('runTests=true')) {
        // 等待系统初始化完成后运行测试
        setTimeout(() => {
            test.run();
        }, 1000);
    }

    console.log('✅ 依赖容器测试用例已加载');

})();
